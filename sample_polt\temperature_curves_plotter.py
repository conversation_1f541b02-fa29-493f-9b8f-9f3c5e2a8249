#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
温度序列数据曲线图绘制工具
用于绘制D:\PycharmProjects\Chemical-Optimization_0715\pso_optimization_model\data\Esterification中21个真实温度序列数据的曲线图
"""

import os
import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

class TemperatureCurvesPlotter:
    def __init__(self, data_dir, output_dir):
        """
        初始化温度曲线绘制器
        
        Args:
            data_dir (str): 数据文件目录路径
            output_dir (str): 输出图片保存目录路径
        """
        self.data_dir = Path(data_dir)
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        # 存储所有温度数据
        self.temperature_data = {}
        
    def load_temperature_data(self):
        """
        加载所有温度序列数据
        """
        print("正在加载温度序列数据...")
        
        # 查找所有Sample_*.xlsx文件
        sample_files = list(self.data_dir.glob("Sample_*.xlsx"))
        sample_files.sort(key=lambda x: int(x.stem.split('_')[1]))
        
        print(f"找到 {len(sample_files)} 个样本文件")
        
        for file_path in sample_files:
            try:
                # 读取Excel文件
                df = pd.read_excel(file_path)
                
                # 提取样本编号
                sample_num = int(file_path.stem.split('_')[1])
                
                # 假设温度数据在第一列或名为'Temperature'的列
                if 'Temperature' in df.columns:
                    temp_data = df['Temperature'].dropna().values
                elif 'temperature' in df.columns:
                    temp_data = df['temperature'].dropna().values
                elif '温度' in df.columns:
                    temp_data = df['温度'].dropna().values
                else:
                    # 如果没有明确的温度列，使用第一列数值数据
                    temp_data = df.iloc[:, 0].dropna().values
                
                # 确保数据是数值类型
                temp_data = pd.to_numeric(temp_data, errors='coerce')
                temp_data = temp_data[~np.isnan(temp_data)]
                
                self.temperature_data[sample_num] = temp_data
                print(f"样本 {sample_num}: 加载了 {len(temp_data)} 个温度数据点")
                
            except Exception as e:
                print(f"加载文件 {file_path} 时出错: {e}")
        
        print(f"成功加载 {len(self.temperature_data)} 个样本的温度数据")
        
    def plot_individual_curves(self):
        """
        绘制每个样本的单独温度曲线图
        """
        print("正在绘制单独的温度曲线图...")
        
        for sample_num, temp_data in self.temperature_data.items():
            plt.figure(figsize=(12, 8))
            
            # 创建时间轴（假设每个数据点代表一个时间单位）
            time_points = np.arange(len(temp_data))
            
            plt.plot(time_points, temp_data, 'b-', linewidth=2, marker='o', markersize=4)
            plt.title(f'样本 {sample_num} 温度序列曲线', fontsize=16, fontweight='bold')
            plt.xlabel('时间点', fontsize=14)
            plt.ylabel('温度 (°C)', fontsize=14)
            plt.grid(True, alpha=0.3)
            
            # 添加统计信息
            mean_temp = np.mean(temp_data)
            max_temp = np.max(temp_data)
            min_temp = np.min(temp_data)
            
            plt.text(0.02, 0.98, f'平均温度: {mean_temp:.2f}°C\n最高温度: {max_temp:.2f}°C\n最低温度: {min_temp:.2f}°C', 
                    transform=plt.gca().transAxes, fontsize=12, verticalalignment='top',
                    bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
            
            plt.tight_layout()
            
            # 保存图片
            output_file = self.output_dir / f'sample_{sample_num}_temperature_curve.png'
            plt.savefig(output_file, dpi=300, bbox_inches='tight')
            plt.close()
            
            print(f"已保存: {output_file}")
    
    def plot_all_curves_overlay(self):
        """
        绘制所有样本的温度曲线叠加图
        """
        print("正在绘制所有样本的叠加温度曲线图...")
        
        plt.figure(figsize=(16, 10))
        
        # 生成颜色映射
        colors = plt.cm.tab20(np.linspace(0, 1, len(self.temperature_data)))
        
        for i, (sample_num, temp_data) in enumerate(sorted(self.temperature_data.items())):
            time_points = np.arange(len(temp_data))
            plt.plot(time_points, temp_data, color=colors[i], linewidth=1.5, 
                    label=f'样本 {sample_num}', alpha=0.8)
        
        plt.title('所有样本温度序列曲线叠加图', fontsize=18, fontweight='bold')
        plt.xlabel('时间点', fontsize=14)
        plt.ylabel('温度 (°C)', fontsize=14)
        plt.grid(True, alpha=0.3)
        plt.legend(bbox_to_anchor=(1.05, 1), loc='upper left', fontsize=10)
        
        plt.tight_layout()
        
        # 保存图片
        output_file = self.output_dir / 'all_samples_temperature_curves_overlay.png'
        plt.savefig(output_file, dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"已保存: {output_file}")
    
    def plot_statistics_summary(self):
        """
        绘制温度统计摘要图
        """
        print("正在绘制温度统计摘要图...")
        
        # 计算统计数据
        sample_nums = []
        mean_temps = []
        max_temps = []
        min_temps = []
        std_temps = []
        
        for sample_num, temp_data in sorted(self.temperature_data.items()):
            sample_nums.append(sample_num)
            mean_temps.append(np.mean(temp_data))
            max_temps.append(np.max(temp_data))
            min_temps.append(np.min(temp_data))
            std_temps.append(np.std(temp_data))
        
        # 创建子图
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
        
        # 平均温度
        ax1.bar(sample_nums, mean_temps, color='skyblue', alpha=0.7)
        ax1.set_title('各样本平均温度', fontsize=14, fontweight='bold')
        ax1.set_xlabel('样本编号')
        ax1.set_ylabel('平均温度 (°C)')
        ax1.grid(True, alpha=0.3)
        
        # 最高和最低温度
        ax2.plot(sample_nums, max_temps, 'ro-', label='最高温度', linewidth=2)
        ax2.plot(sample_nums, min_temps, 'bo-', label='最低温度', linewidth=2)
        ax2.set_title('各样本最高/最低温度', fontsize=14, fontweight='bold')
        ax2.set_xlabel('样本编号')
        ax2.set_ylabel('温度 (°C)')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        # 温度标准差
        ax3.bar(sample_nums, std_temps, color='lightcoral', alpha=0.7)
        ax3.set_title('各样本温度标准差', fontsize=14, fontweight='bold')
        ax3.set_xlabel('样本编号')
        ax3.set_ylabel('标准差 (°C)')
        ax3.grid(True, alpha=0.3)
        
        # 温度分布箱线图
        temp_values = [temp_data for temp_data in self.temperature_data.values()]
        ax4.boxplot(temp_values, labels=sample_nums)
        ax4.set_title('各样本温度分布箱线图', fontsize=14, fontweight='bold')
        ax4.set_xlabel('样本编号')
        ax4.set_ylabel('温度 (°C)')
        ax4.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        # 保存图片
        output_file = self.output_dir / 'temperature_statistics_summary.png'
        plt.savefig(output_file, dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"已保存: {output_file}")
    
    def generate_report(self):
        """
        生成数据分析报告
        """
        print("正在生成数据分析报告...")
        
        report_lines = []
        report_lines.append("=" * 60)
        report_lines.append("温度序列数据分析报告")
        report_lines.append("=" * 60)
        report_lines.append(f"数据源: {self.data_dir}")
        report_lines.append(f"样本数量: {len(self.temperature_data)}")
        report_lines.append("")
        
        # 详细统计信息
        report_lines.append("各样本详细统计信息:")
        report_lines.append("-" * 60)
        report_lines.append(f"{'样本编号':<8} {'数据点数':<10} {'平均温度':<10} {'最高温度':<10} {'最低温度':<10} {'标准差':<10}")
        report_lines.append("-" * 60)
        
        for sample_num, temp_data in sorted(self.temperature_data.items()):
            mean_temp = np.mean(temp_data)
            max_temp = np.max(temp_data)
            min_temp = np.min(temp_data)
            std_temp = np.std(temp_data)
            
            report_lines.append(f"{sample_num:<8} {len(temp_data):<10} {mean_temp:<10.2f} {max_temp:<10.2f} {min_temp:<10.2f} {std_temp:<10.2f}")
        
        # 保存报告
        report_file = self.output_dir / 'temperature_analysis_report.txt'
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write('\n'.join(report_lines))
        
        print(f"已保存分析报告: {report_file}")
    
    def run_analysis(self):
        """
        运行完整的温度数据分析和绘图
        """
        print("开始温度序列数据分析...")
        
        # 加载数据
        self.load_temperature_data()
        
        if not self.temperature_data:
            print("错误: 没有找到有效的温度数据!")
            return
        
        # 绘制各种图表
        self.plot_individual_curves()
        self.plot_all_curves_overlay()
        self.plot_statistics_summary()
        
        # 生成报告
        self.generate_report()
        
        print(f"\n分析完成! 所有图表和报告已保存到: {self.output_dir}")
        print(f"共处理了 {len(self.temperature_data)} 个样本的温度数据")

def main():
    """
    主函数
    """
    # 设置路径
    data_dir = r"D:\PycharmProjects\Chemical-Optimization_0715\pso_optimization_model\data\Esterification"
    output_dir = r"D:\PycharmProjects\Chemical-Optimization_0715\pso_optimization_model\sample_polt"
    
    # 创建绘图器并运行分析
    plotter = TemperatureCurvesPlotter(data_dir, output_dir)
    plotter.run_analysis()

if __name__ == "__main__":
    main()

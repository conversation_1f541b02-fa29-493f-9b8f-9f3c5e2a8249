# 混合适应度评估功能使用示例

## 快速开始

### 1. 训练分类器（如果还没有训练）
```bash
# 训练分类器和特征提取器
python main.py --mode train --data-dir data/Esterification --output-dir models
```

### 2. 使用混合适应度评估进行优化
```bash
# 基本使用（默认权重：分类器60%，统计学40%）
python main.py --mode optimize --skip-training --enable-hybrid-fitness

# 自定义权重
python main.py --mode optimize --skip-training --enable-hybrid-fitness \
       --classifier-weight 0.7 --statistical-weight 0.3

# 完整参数示例
python main.py --mode optimize --skip-training \
       --max-iterations 200 --swarm-size 21 \
       --enable-hybrid-fitness \
       --classifier-weight 0.6 --statistical-weight 0.4 \
       --verbose
```

## 详细使用场景

### 场景1: 高质量分类器场景
当您有一个训练良好的分类器时，可以给予更高的权重：

```bash
# 分类器权重80%，统计学权重20%
python main.py --mode optimize --skip-training \
       --enable-hybrid-fitness \
       --classifier-weight 0.8 --statistical-weight 0.2 \
       --max-iterations 300 --swarm-size 30
```

**适用情况**：
- 分类器在验证集上表现优秀（准确率>90%）
- 有大量高质量的训练数据
- 分类器能够很好地区分温度序列质量

### 场景2: 保守评估场景
当分类器质量不确定时，使用平衡的权重：

```bash
# 分类器权重50%，统计学权重50%
python main.py --mode optimize --skip-training \
       --enable-hybrid-fitness \
       --classifier-weight 0.5 --statistical-weight 0.5 \
       --max-iterations 250 --swarm-size 25
```

**适用情况**：
- 分类器训练数据有限
- 对分类器性能不完全确定
- 希望平衡两种评估方法

### 场景3: 统计学主导场景
当更信任统计学方法时：

```bash
# 分类器权重30%，统计学权重70%
python main.py --mode optimize --skip-training \
       --enable-hybrid-fitness \
       --classifier-weight 0.3 --statistical-weight 0.7 \
       --max-iterations 200 --swarm-size 20
```

**适用情况**：
- 分类器训练数据质量一般
- 统计学方法已经验证有效
- 希望以统计学为主，分类器为辅

### 场景4: 纯统计学场景
完全禁用分类器评估：

```bash
# 仅使用统计学方法
python main.py --mode optimize --skip-training \
       --disable-hybrid-fitness \
       --max-iterations 200 --swarm-size 20
```

**适用情况**：
- 没有训练好的分类器
- 分类器性能不佳
- 希望使用原始的统计学方法

### 场景5: 自定义模型目录
使用不同的模型目录：

```bash
# 使用自定义模型目录
python main.py --mode optimize --skip-training \
       --enable-hybrid-fitness \
       --hybrid-model-dir custom_models \
       --classifier-weight 0.6 --statistical-weight 0.4
```

**适用情况**：
- 有多个版本的分类器模型
- 模型存储在非默认位置
- 需要测试不同的模型版本

## 代码中的使用方法

### 基本使用
```python
from src.advanced_pso_optimizer import AdvancedPSOOptimizer

# 创建优化器（启用混合适应度评估）
optimizer = AdvancedPSOOptimizer(
    config_path="config/config.yaml",
    enable_hybrid_fitness=True
)

# 设置优化参数
optimizer.swarm_size = 21
optimizer.max_iterations = 200
optimizer.control_points = 50

# 执行优化
results = optimizer.optimize_advanced()

print(f"最佳适应度: {results['best_fitness']:.6f}")
print(f"迭代次数: {results['total_iterations']}")
```

### 权重调整
```python
# 创建优化器
optimizer = AdvancedPSOOptimizer(enable_hybrid_fitness=True)

# 调整权重（分类器70%，统计学30%）
optimizer.set_hybrid_fitness_weights(
    classifier_weight=0.7,
    statistical_weight=0.3
)

# 执行优化
results = optimizer.optimize_advanced()

# 查看适应度分解
if 'fitness_breakdown' in results:
    breakdown = results['fitness_breakdown']
    print(f"分类器适应度: {breakdown.get('classifier_fitness', 0):.4f}")
    print(f"统计学适应度: {breakdown.get('statistical_fitness', 0):.4f}")
    print(f"最终适应度: {breakdown.get('final_fitness', 0):.4f}")
```

### 性能监控
```python
# 执行优化
results = optimizer.optimize_advanced()

# 获取性能统计
stats = optimizer.get_hybrid_fitness_stats()

print("性能统计:")
print(f"  分类器可用: {stats.get('classifier_available', False)}")
print(f"  混合评估启用: {stats.get('hybrid_enabled', False)}")

# 缓存统计
cache_stats = stats.get('cache_stats', {})
if cache_stats.get('hits', 0) + cache_stats.get('misses', 0) > 0:
    print(f"  缓存命中率: {cache_stats.get('hit_rate', 0):.1%}")
    print(f"  缓存大小: {cache_stats.get('cache_size', 0)}")

# 评估时间统计
eval_times = stats.get('evaluation_times', {})
for eval_type, times in eval_times.items():
    if times.get('count', 0) > 0:
        print(f"  {eval_type}评估时间: {times.get('mean', 0)*1000:.2f}ms")
```

### 动态控制
```python
# 创建优化器
optimizer = AdvancedPSOOptimizer()

# 动态启用混合适应度评估
optimizer.enable_hybrid_fitness_evaluation(True)

# 执行一轮优化
results1 = optimizer.optimize_advanced()

# 调整权重
optimizer.set_hybrid_fitness_weights(0.8, 0.2)

# 清空缓存
optimizer.clear_hybrid_fitness_cache()

# 执行另一轮优化
results2 = optimizer.optimize_advanced()

# 比较结果
print(f"第一轮适应度: {results1['best_fitness']:.6f}")
print(f"第二轮适应度: {results2['best_fitness']:.6f}")
```

## 结果分析

### 输出信息解读
运行混合适应度评估后，您会看到类似以下的输出：

```
✅ 高级PSO优化完成！
  最佳适应度: 0.892345
  迭代次数: 156
  序列长度: 41,296
  温度范围: 42.68°C → 152.00°C
  温度上升: 109.32°C
  是否收敛: 是
  结果文件: results/advanced_temperature_sequence_20250723_024303.csv
  报告文件: results/advanced_pso_report_20250723_024303.txt

📊 混合适应度评估统计:
  分类器可用: 是
  混合评估: 启用
  缓存命中率: 85.2%
  缓存大小: 234
  平均评估时间: 12.34ms

🔍 适应度分解:
  分类器适应度: 0.8756 (权重: 60.0%)
  统计学适应度: 0.9123 (权重: 40.0%)
  最终适应度: 0.8924
  温度上升分数: 0.9999
  相似性分数: 0.8306
  趋势分数: 0.9999
  阶段分数: 0.9702
  平滑性分数: 0.4014
```

### 关键指标说明

1. **分类器可用**: 表示分类器模型是否成功加载
2. **混合评估**: 表示是否启用了混合适应度评估
3. **缓存命中率**: 缓存系统的效率，越高越好
4. **分类器适应度**: 基于分类学习的评估结果
5. **统计学适应度**: 基于统计学方法的评估结果
6. **最终适应度**: 加权平均后的最终结果

## 性能优化建议

### 1. 缓存优化
```python
# 在配置文件中调整缓存设置
pso:
  hybrid_fitness:
    classifier:
      enable_cache: true
      cache_size: 2000  # 增加缓存大小
```

### 2. 序列长度优化
```python
# 限制序列长度以提高性能
pso:
  hybrid_fitness:
    performance:
      max_sequence_length: 30000  # 减少最大序列长度
```

### 3. 评估策略优化
```python
# 调整评估策略
pso:
  hybrid_fitness:
    classifier:
      evaluation_strategy: "multiple"  # 使用更快的评估策略
      num_comparisons: 3  # 减少比较次数
```

## 故障排除

### 常见问题及解决方案

1. **分类器加载失败**
```bash
# 检查模型文件是否存在
ls -la models/
# 应该看到: sequence_classifier.joblib, feature_extractor.joblib

# 如果文件不存在，重新训练
python main.py --mode train
```

2. **性能过慢**
```bash
# 使用较小的参数进行测试
python main.py --mode optimize --skip-training \
       --enable-hybrid-fitness \
       --max-iterations 50 --swarm-size 10 \
       --control-points 20
```

3. **权重设置无效**
```bash
# 确保权重为正数且总和不为零
python main.py --mode optimize --skip-training \
       --enable-hybrid-fitness \
       --classifier-weight 0.6 --statistical-weight 0.4  # 正确
       # --classifier-weight -0.1 --statistical-weight 0.5  # 错误：负数
       # --classifier-weight 0.0 --statistical-weight 0.0   # 错误：总和为零
```

4. **内存不足**
```bash
# 减少粒子群大小和控制点数量
python main.py --mode optimize --skip-training \
       --enable-hybrid-fitness \
       --swarm-size 15 --control-points 30
```

## 最佳实践总结

1. **首次使用**: 使用默认权重(60%/40%)开始
2. **权重调整**: 根据分类器性能逐步调整权重
3. **性能监控**: 定期检查缓存命中率和评估时间
4. **渐进优化**: 从小参数开始，逐步增加复杂度
5. **结果验证**: 对比混合评估和纯统计学方法的结果
6. **定期清理**: 长时间运行时清空缓存以释放内存

通过以上示例和指导，您可以充分利用混合适应度评估功能，获得更准确和可靠的温度序列优化结果。

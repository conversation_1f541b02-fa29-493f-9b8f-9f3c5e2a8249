#!/usr/bin/env python3
"""
LSTM兼容性修复脚本

该脚本用于修复旧版本模型文件的兼容性问题，特别是：
1. 为旧版本的LSTM模型添加缺失的device属性
2. 重新保存模型文件以确保兼容性
3. 验证修复效果
"""

import os
import sys
import joblib
import torch
import logging
from pathlib import Path

# 添加src目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger('fix_lstm_compatibility')


def backup_model_files(model_dir="models"):
    """备份现有的模型文件"""
    model_dir = Path(model_dir)
    if not model_dir.exists():
        logger.warning(f"模型目录不存在: {model_dir}")
        return False
    
    backup_dir = model_dir / "backup"
    backup_dir.mkdir(exist_ok=True)
    
    model_files = [
        "feature_extractor.joblib",
        "sequence_classifier.joblib",
        "sequence_classifier_metadata.joblib"
    ]
    
    backed_up = []
    for file_name in model_files:
        file_path = model_dir / file_name
        if file_path.exists():
            backup_path = backup_dir / f"{file_name}.backup"
            try:
                import shutil
                shutil.copy2(file_path, backup_path)
                backed_up.append(file_name)
                logger.info(f"已备份: {file_name} -> {backup_path}")
            except Exception as e:
                logger.error(f"备份失败 {file_name}: {e}")
                return False
    
    if backed_up:
        logger.info(f"成功备份 {len(backed_up)} 个模型文件")
        return True
    else:
        logger.warning("没有找到需要备份的模型文件")
        return False


def fix_feature_extractor(model_path="models/feature_extractor.joblib"):
    """修复特征提取器的兼容性问题"""
    if not os.path.exists(model_path):
        logger.warning(f"特征提取器文件不存在: {model_path}")
        return False
    
    try:
        logger.info(f"加载特征提取器: {model_path}")
        feature_extractor = joblib.load(model_path)
        
        # 检查并修复FeatureExtractor的属性
        fixed_attributes = []
        
        if not hasattr(feature_extractor, 'device'):
            feature_extractor.device = torch.device('cpu')
            fixed_attributes.append('device')
        
        if not hasattr(feature_extractor, 'enable_gpu_memory_optimization'):
            feature_extractor.enable_gpu_memory_optimization = False
            fixed_attributes.append('enable_gpu_memory_optimization')
        
        if not hasattr(feature_extractor, 'variable_length_enabled'):
            feature_extractor.variable_length_enabled = False
            fixed_attributes.append('variable_length_enabled')
        
        if not hasattr(feature_extractor, 'max_sequence_length'):
            feature_extractor.max_sequence_length = 10000
            fixed_attributes.append('max_sequence_length')
        
        # 检查并修复LSTM提取器的属性
        if hasattr(feature_extractor, 'lstm_extractor') and feature_extractor.lstm_extractor is not None:
            lstm_extractor = feature_extractor.lstm_extractor
            
            if not hasattr(lstm_extractor, 'device'):
                lstm_extractor.device = torch.device('cpu')
                fixed_attributes.append('lstm_extractor.device')
            
            if not hasattr(lstm_extractor, 'hidden_size'):
                # 尝试从配置或默认值获取
                if hasattr(feature_extractor, 'feature_config'):
                    lstm_extractor.hidden_size = feature_extractor.feature_config.get('lstm_features', {}).get('hidden_size', 64)
                else:
                    lstm_extractor.hidden_size = 64
                fixed_attributes.append('lstm_extractor.hidden_size')
            
            if not hasattr(lstm_extractor, 'num_layers'):
                if hasattr(feature_extractor, 'feature_config'):
                    lstm_extractor.num_layers = feature_extractor.feature_config.get('lstm_features', {}).get('num_layers', 2)
                else:
                    lstm_extractor.num_layers = 2
                fixed_attributes.append('lstm_extractor.num_layers')
            
            # 确保LSTM模型在CPU上（避免设备不匹配问题）
            try:
                lstm_extractor.to(torch.device('cpu'))
                lstm_extractor.device = torch.device('cpu')
                logger.info("LSTM模型已移动到CPU")
            except Exception as e:
                logger.warning(f"移动LSTM模型到CPU失败: {e}")
        
        if fixed_attributes:
            logger.info(f"修复了以下属性: {', '.join(fixed_attributes)}")
            
            # 重新保存修复后的模型
            joblib.dump(feature_extractor, model_path)
            logger.info(f"已保存修复后的特征提取器: {model_path}")
            return True
        else:
            logger.info("特征提取器无需修复")
            return True
            
    except Exception as e:
        logger.error(f"修复特征提取器失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_fixed_models():
    """测试修复后的模型是否正常工作"""
    logger.info("测试修复后的模型...")
    
    try:
        # 测试特征提取器
        from feature_extractor import FeatureExtractor
        import numpy as np
        
        # 创建测试序列
        test_sequence = np.linspace(20, 150, 1000)
        
        # 创建特征提取器
        extractor = FeatureExtractor(config_path="config/config.yaml")
        
        # 测试单序列特征提取
        features = extractor.extract_sequence_features(test_sequence)
        logger.info(f"✅ 单序列特征提取测试通过，提取了 {len(features)} 个特征")

        # 测试成对特征提取
        test_sequence2 = test_sequence + np.random.normal(0, 1, len(test_sequence))
        pairwise_features = extractor.extract_pairwise_features(test_sequence, test_sequence2)
        logger.info(f"✅ 成对特征提取测试通过，提取了 {len(pairwise_features)} 个特征")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 模型测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主修复流程"""
    logger.info("🔧 开始LSTM兼容性修复")
    
    # 1. 备份现有模型文件
    logger.info("步骤1: 备份现有模型文件")
    backup_success = backup_model_files()
    
    # 2. 修复特征提取器
    logger.info("步骤2: 修复特征提取器兼容性")
    fix_success = fix_feature_extractor()
    
    # 3. 测试修复效果
    logger.info("步骤3: 测试修复效果")
    test_success = test_fixed_models()
    
    # 4. 输出结果
    logger.info("=" * 60)
    logger.info("修复结果总结")
    logger.info("=" * 60)
    
    results = {
        "备份模型文件": "✅ 成功" if backup_success else "⚠️ 跳过或失败",
        "修复特征提取器": "✅ 成功" if fix_success else "❌ 失败",
        "测试修复效果": "✅ 成功" if test_success else "❌ 失败"
    }
    
    for step, result in results.items():
        logger.info(f"  {step}: {result}")
    
    overall_success = fix_success and test_success
    
    if overall_success:
        logger.info("\n🎉 LSTM兼容性修复完成！")
        logger.info("现在可以正常使用混合适应度评估功能了。")
        logger.info("\n建议运行以下命令测试:")
        logger.info("python main.py --mode optimize --skip-training --enable-hybrid-fitness --max-iterations 10 --swarm-size 5")
    else:
        logger.error("\n❌ 修复过程中出现问题")
        logger.error("请检查错误信息并手动处理")
        
        if backup_success:
            logger.info("\n💡 如需恢复原始文件，可以从 models/backup/ 目录恢复")
    
    return overall_success


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

#!/usr/bin/env python3
"""
混合适应度评估功能使用示例

该脚本展示了如何使用新的混合适应度评估功能，包括：
1. 基本使用方法
2. 权重调整
3. 性能监控
4. 配置选项
"""

import os
import sys
import numpy as np
from datetime import datetime

# 添加src目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.advanced_pso_optimizer import AdvancedPSOOptimizer


def example_1_basic_usage():
    """示例1: 基本使用方法"""
    print("=" * 60)
    print("示例1: 基本使用混合适应度评估")
    print("=" * 60)
    
    # 创建启用混合适应度评估的优化器
    optimizer = AdvancedPSOOptimizer(
        config_path="config/config.yaml",
        model_dir="models",
        enable_hybrid_fitness=True  # 启用混合适应度评估
    )
    
    # 设置较小的参数进行演示
    optimizer.swarm_size = 10
    optimizer.max_iterations = 20
    optimizer.control_points = 20
    
    print("开始优化...")
    results = optimizer.optimize_advanced()
    
    print(f"✅ 优化完成！")
    print(f"  最佳适应度: {results['best_fitness']:.6f}")
    print(f"  迭代次数: {results['total_iterations']}")
    
    # 显示混合适应度统计
    if 'hybrid_fitness_stats' in results:
        stats = results['hybrid_fitness_stats']
        print(f"  分类器可用: {'是' if stats.get('classifier_available') else '否'}")
        cache_stats = stats.get('cache_stats', {})
        if cache_stats.get('hits', 0) + cache_stats.get('misses', 0) > 0:
            print(f"  缓存命中率: {cache_stats.get('hit_rate', 0):.1%}")
    
    return results


def example_2_weight_adjustment():
    """示例2: 权重调整"""
    print("\n" + "=" * 60)
    print("示例2: 调整混合适应度权重")
    print("=" * 60)
    
    # 创建优化器
    optimizer = AdvancedPSOOptimizer(
        config_path="config/config.yaml",
        enable_hybrid_fitness=True
    )
    
    # 调整权重：更重视分类器评估
    optimizer.set_hybrid_fitness_weights(
        classifier_weight=0.8,  # 80%
        statistical_weight=0.2  # 20%
    )
    
    print("权重已调整为: 分类器80%, 统计学20%")
    
    # 设置参数并运行
    optimizer.swarm_size = 8
    optimizer.max_iterations = 15
    optimizer.control_points = 15
    
    print("开始优化...")
    results = optimizer.optimize_advanced()
    
    print(f"✅ 优化完成！")
    print(f"  最佳适应度: {results['best_fitness']:.6f}")
    
    # 显示适应度分解
    if 'fitness_breakdown' in results:
        breakdown = results['fitness_breakdown']
        if 'classifier_fitness' in breakdown:
            print(f"  分类器适应度: {breakdown['classifier_fitness']:.4f}")
        if 'statistical_fitness' in breakdown:
            print(f"  统计学适应度: {breakdown['statistical_fitness']:.4f}")
    
    return results


def example_3_performance_monitoring():
    """示例3: 性能监控"""
    print("\n" + "=" * 60)
    print("示例3: 性能监控和统计")
    print("=" * 60)
    
    # 创建优化器
    optimizer = AdvancedPSOOptimizer(
        config_path="config/config.yaml",
        enable_hybrid_fitness=True
    )
    
    # 运行优化
    optimizer.swarm_size = 6
    optimizer.max_iterations = 10
    optimizer.control_points = 10
    
    print("开始优化...")
    results = optimizer.optimize_advanced()
    
    # 获取详细的性能统计
    performance_stats = optimizer.get_hybrid_fitness_stats()
    
    print(f"✅ 优化完成！")
    print(f"  最佳适应度: {results['best_fitness']:.6f}")
    
    print(f"\n📊 详细性能统计:")
    print(f"  分类器可用: {'是' if performance_stats.get('classifier_available') else '否'}")
    print(f"  混合评估启用: {'是' if performance_stats.get('hybrid_enabled') else '否'}")
    
    # 缓存统计
    cache_stats = performance_stats.get('cache_stats', {})
    print(f"  缓存命中: {cache_stats.get('hits', 0)}")
    print(f"  缓存未命中: {cache_stats.get('misses', 0)}")
    print(f"  缓存命中率: {cache_stats.get('hit_rate', 0):.1%}")
    print(f"  当前缓存大小: {cache_stats.get('cache_size', 0)}")
    
    # 评估时间统计
    eval_times = performance_stats.get('evaluation_times', {})
    for eval_type, times in eval_times.items():
        if times.get('count', 0) > 0:
            print(f"  {eval_type}评估时间: 平均{times.get('mean', 0)*1000:.2f}ms, "
                  f"最大{times.get('max', 0)*1000:.2f}ms")
    
    return results


def example_4_fallback_behavior():
    """示例4: 降级行为演示"""
    print("\n" + "=" * 60)
    print("示例4: 分类器不可用时的降级行为")
    print("=" * 60)
    
    # 创建使用不存在模型目录的优化器（模拟分类器不可用）
    optimizer = AdvancedPSOOptimizer(
        config_path="config/config.yaml",
        model_dir="nonexistent_models",  # 不存在的目录
        enable_hybrid_fitness=True
    )
    
    # 运行优化
    optimizer.swarm_size = 5
    optimizer.max_iterations = 8
    optimizer.control_points = 10
    
    print("开始优化（分类器不可用，应自动降级到统计学方法）...")
    results = optimizer.optimize_advanced()
    
    print(f"✅ 优化完成！")
    print(f"  最佳适应度: {results['best_fitness']:.6f}")
    
    # 检查是否正确降级
    if 'hybrid_fitness_stats' in results:
        stats = results['hybrid_fitness_stats']
        classifier_available = stats.get('classifier_available', False)
        print(f"  分类器可用: {'是' if classifier_available else '否'}")
        print(f"  降级状态: {'已降级到纯统计学方法' if not classifier_available else '正常使用混合方法'}")
    
    return results


def example_5_command_line_equivalent():
    """示例5: 等效的命令行调用"""
    print("\n" + "=" * 60)
    print("示例5: 等效的命令行调用方法")
    print("=" * 60)
    
    print("以下是等效的命令行调用方法：")
    print()
    
    print("1. 启用混合适应度评估（默认权重）：")
    print("   python main.py --mode optimize --skip-training --enable-hybrid-fitness")
    print()
    
    print("2. 自定义权重：")
    print("   python main.py --mode optimize --skip-training --enable-hybrid-fitness \\")
    print("          --classifier-weight 0.7 --statistical-weight 0.3")
    print()
    
    print("3. 禁用混合适应度评估：")
    print("   python main.py --mode optimize --skip-training --disable-hybrid-fitness")
    print()
    
    print("4. 使用自定义模型目录：")
    print("   python main.py --mode optimize --skip-training --enable-hybrid-fitness \\")
    print("          --hybrid-model-dir custom_models")
    print()
    
    print("5. 完整参数示例：")
    print("   python main.py --mode optimize --skip-training \\")
    print("          --max-iterations 200 --swarm-size 21 \\")
    print("          --enable-hybrid-fitness \\")
    print("          --classifier-weight 0.6 --statistical-weight 0.4 \\")
    print("          --verbose")


def main():
    """主函数"""
    print("🚀 混合适应度评估功能使用示例")
    print(f"示例运行时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        # 运行所有示例
        example_1_basic_usage()
        example_2_weight_adjustment()
        example_3_performance_monitoring()
        example_4_fallback_behavior()
        example_5_command_line_equivalent()
        
        print("\n" + "=" * 60)
        print("🎉 所有示例运行完成！")
        print("=" * 60)
        print()
        print("💡 提示:")
        print("  - 混合适应度评估结合了分类器和统计学两种方法")
        print("  - 默认权重为分类器60%，统计学40%")
        print("  - 当分类器不可用时，会自动降级到纯统计学方法")
        print("  - 可以通过命令行参数或代码调整权重")
        print("  - 缓存机制可以提高重复评估的性能")
        print()
        print("📚 更多信息请参考:")
        print("  - config/config.yaml 中的 hybrid_fitness 配置")
        print("  - src/hybrid_fitness_evaluator.py 的详细实现")
        print("  - test_hybrid_fitness.py 的测试用例")
        
    except Exception as e:
        print(f"❌ 示例运行失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

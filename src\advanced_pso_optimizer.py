#!/usr/bin/env python3
"""
深度优化的PSO温度序列优化器

主要改进：
1. 更精细的适应度函数设计
2. 自适应参数调整
3. 多样性保持机制
4. 更真实的温度变化模式生成
5. 动态约束调整
"""

import numpy as np
import pandas as pd
import yaml
import logging
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Callable
import os
from datetime import datetime

try:
    from .business_data_analyzer import BusinessDataAnalyzer
    from .data_driven_initializer import DataDrivenInitializer
    from .enhanced_constraints import EnhancedConstraints
    from .hybrid_fitness_evaluator import HybridFitnessEvaluator
except ImportError:
    from business_data_analyzer import BusinessDataAnalyzer
    from data_driven_initializer import DataDrivenInitializer
    from enhanced_constraints import EnhancedConstraints
    from hybrid_fitness_evaluator import HybridFitnessEvaluator


class AdvancedPSOOptimizer:
    """深度优化的PSO优化器"""
    
    def __init__(self, config_path: str = "config/config.yaml",
                 model_dir: str = "models",
                 enable_hybrid_fitness: bool = None):
        """
        初始化深度优化PSO优化器

        Args:
            config_path: 配置文件路径
            model_dir: 模型文件目录
            enable_hybrid_fitness: 是否启用混合适应度评估（None表示使用配置文件设置）
        """
        self.logger = logging.getLogger(__name__)

        # 加载配置
        self.config = self._load_config(config_path)
        self.config_path = config_path
        self.model_dir = model_dir
        
        # PSO参数 - 更激进的设置
        self.swarm_size = self.config.get('pso', {}).get('swarm_size', 50)  # 增加粒子数
        self.max_iterations = self.config.get('pso', {}).get('max_iterations', 300)  # 增加迭代数
        self.control_points = self.config.get('temperature_sequence', {}).get('control_points', 50)  # 增加控制点
        
        # 自适应参数
        self.w_start = 0.9  # 初始惯性权重
        self.w_end = 0.1    # 最终惯性权重
        self.c1_start = 2.5  # 初始个体学习因子
        self.c1_end = 0.5    # 最终个体学习因子
        self.c2_start = 0.5  # 初始社会学习因子
        self.c2_end = 2.5    # 最终社会学习因子
        
        # 多样性保持参数
        self.diversity_threshold = 0.1
        self.mutation_probability = 0.1
        self.mutation_strength = 0.05
        
        # 温度序列参数
        self.sequence_length = self.config.get('temperature_sequence', {}).get('sequence_length', 50000)
        self.min_temp = self.config.get('temperature_sequence', {}).get('min_temperature', 13.0)
        self.max_temp = self.config.get('temperature_sequence', {}).get('max_temperature', 152.0)
        
        # 变长序列支持
        self.variable_length = self.config.get('temperature_sequence', {}).get('variable_length', {})
        self.enable_variable_length = self.variable_length.get('enable', True)
        self.min_length = self.variable_length.get('min_length', 18808)
        self.max_length = self.variable_length.get('max_length', 92002)
        
        # 混合适应度评估配置
        self.hybrid_config = self.config.get('pso', {}).get('hybrid_fitness', {})
        if enable_hybrid_fitness is not None:
            self.enable_hybrid_fitness = enable_hybrid_fitness
        else:
            self.enable_hybrid_fitness = self.hybrid_config.get('enable', True)

        # 初始化组件
        self.business_analyzer = None
        self.data_initializer = None
        self.constraints = None
        self.hybrid_fitness_evaluator = None

        # 粒子群
        self.swarm = []
        self.global_best_position = None
        self.global_best_fitness = float('-inf')
        self.fitness_history = []
        self.diversity_history = []

        # 真实数据缓存
        self.real_sequences = None
        self.real_statistics = None
        
        self.logger.info(f"深度优化PSO优化器初始化完成")
        self.logger.info(f"粒子群大小: {self.swarm_size}, 最大迭代: {self.max_iterations}")
        self.logger.info(f"控制点数量: {self.control_points}")
    
    def _load_config(self, config_path: str) -> Dict:
        """加载配置文件"""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except Exception as e:
            self.logger.warning(f"无法加载配置文件 {config_path}: {e}")
            return {}
    
    def _initialize_components(self):
        """初始化各个组件"""
        if self.business_analyzer is None:
            self.logger.info("初始化业务数据分析器...")
            self.business_analyzer = BusinessDataAnalyzer()

        if self.data_initializer is None:
            self.logger.info("初始化数据驱动初始化器...")
            self.data_initializer = DataDrivenInitializer()

        if self.constraints is None:
            self.logger.info("初始化增强约束系统...")
            self.constraints = EnhancedConstraints()

        # 初始化混合适应度评估器（即使没有参考序列也可以初始化）
        if self.enable_hybrid_fitness and self.hybrid_fitness_evaluator is None:
            self.logger.info("初始化混合适应度评估器...")
            try:
                # 获取当前可用的参考序列
                current_reference_sequences = []
                if hasattr(self, 'real_sequences') and self.real_sequences:
                    current_reference_sequences = self.real_sequences

                self.hybrid_fitness_evaluator = HybridFitnessEvaluator(
                    config_path=self.config_path,
                    model_dir=self.model_dir,
                    statistical_fitness_function=self.advanced_fitness_function,
                    reference_sequences=current_reference_sequences
                )
                self.logger.info("混合适应度评估器初始化成功")

                if len(current_reference_sequences) == 0:
                    self.logger.info("混合适应度评估器将在真实数据加载后获得参考序列")

            except Exception as e:
                self.logger.error(f"混合适应度评估器初始化失败: {e}")
                self.logger.warning("将使用纯统计学适应度评估方法")
                self.enable_hybrid_fitness = False
    
    def _load_real_data_statistics(self):
        """加载真实数据统计信息"""
        if self.real_sequences is None:
            self.logger.info("加载真实温度数据...")
            
            # 加载真实数据
            data_dir = self.config.get('data', {}).get('data_dir', 'data/Esterification')
            self.real_sequences = []
            
            for sample_id in range(1, 22):
                sample_file = os.path.join(data_dir, f"Sample_{sample_id}.xlsx")
                try:
                    df = pd.read_excel(sample_file, header=None)
                    temp_sequence = df.iloc[:, 0].values
                    temp_sequence = temp_sequence[~np.isnan(temp_sequence)]
                    
                    if len(temp_sequence) > 1000:  # 确保有足够的数据点
                        self.real_sequences.append(temp_sequence)
                        self.logger.info(f"加载样本 {sample_id}，长度: {len(temp_sequence):,}")
                
                except Exception as e:
                    self.logger.warning(f"无法加载样本 {sample_id}: {e}")
            
            # 计算统计信息
            self._compute_real_statistics()
    
    def _compute_real_statistics(self):
        """计算真实数据的详细统计信息"""
        if not self.real_sequences:
            return
        
        self.real_statistics = {
            'rises': [seq[-1] - seq[0] for seq in self.real_sequences],
            'means': [np.mean(seq) for seq in self.real_sequences],
            'stds': [np.std(seq) for seq in self.real_sequences],
            'lengths': [len(seq) for seq in self.real_sequences],
            'slopes': [],
            'volatilities': [],
            'stage_patterns': []
        }
        
        # 计算更复杂的统计特征
        for seq in self.real_sequences:
            # 斜率
            x = np.arange(len(seq))
            slope = np.polyfit(x, seq, 1)[0]
            self.real_statistics['slopes'].append(slope)
            
            # 波动性（变化率的标准差）
            changes = np.diff(seq)
            volatility = np.std(changes)
            self.real_statistics['volatilities'].append(volatility)
            
            # 阶段模式（分为5个阶段的平均温度）
            stage_size = len(seq) // 5
            stages = []
            for i in range(5):
                start_idx = i * stage_size
                end_idx = (i + 1) * stage_size if i < 4 else len(seq)
                stage_mean = np.mean(seq[start_idx:end_idx])
                stages.append(stage_mean)
            self.real_statistics['stage_patterns'].append(stages)
        
        self.logger.info(f"真实数据统计计算完成，样本数: {len(self.real_sequences)}")

        # 更新混合适应度评估器的参考序列
        if self.enable_hybrid_fitness and self.hybrid_fitness_evaluator:
            try:
                self.hybrid_fitness_evaluator.set_reference_sequences(self.real_sequences)
                self.logger.info("混合适应度评估器的参考序列已更新")
            except Exception as e:
                self.logger.warning(f"更新混合适应度评估器参考序列失败: {e}")
    
    class Particle:
        """粒子类"""
        def __init__(self, control_points: int):
            self.position = np.random.uniform(-1, 1, control_points)
            self.velocity = np.random.uniform(-0.1, 0.1, control_points)
            self.best_position = self.position.copy()
            self.fitness = float('-inf')
            self.best_fitness = float('-inf')
            self.stagnation_count = 0  # 停滞计数
        
        def update_best(self):
            """更新个体最佳"""
            if self.fitness > self.best_fitness:
                self.best_fitness = self.fitness
                self.best_position = self.position.copy()
                self.stagnation_count = 0
            else:
                self.stagnation_count += 1
    
    def _initialize_swarm(self):
        """初始化粒子群"""
        self.logger.info("初始化粒子群...")
        
        # 使用数据驱动初始化
        if self.data_initializer and self.real_sequences:
            try:
                data_driven_particles = self.data_initializer.generate_data_driven_swarm(
                    self.swarm_size // 2  # 一半使用数据驱动
                )
                
                # 转换为粒子对象
                for i, control_points in enumerate(data_driven_particles):
                    particle = self.Particle(self.control_points)

                    # 确保控制点数量一致
                    if len(control_points) != self.control_points:
                        # 重采样到目标长度
                        indices = np.linspace(0, len(control_points)-1, self.control_points)
                        control_points = np.interp(indices, np.arange(len(control_points)), control_points)

                    particle.position = control_points
                    # 添加小幅随机扰动增加多样性
                    particle.position += np.random.normal(0, 0.02, len(control_points))
                    particle.position = np.clip(particle.position, -1, 1)
                    self.swarm.append(particle)
                
                self.logger.info(f"数据驱动初始化了 {len(data_driven_particles)} 个粒子")
                
            except Exception as e:
                self.logger.warning(f"数据驱动初始化失败: {e}")
        
        # 剩余粒子使用改进的智能初始化策略
        remaining_particles = self.swarm_size - len(self.swarm)
        for i in range(remaining_particles):
            particle = self.Particle(self.control_points)

            # 改进的智能初始化策略
            if self.real_statistics:
                # 基于真实数据的统计特征进行初始化
                avg_rise = np.mean(self.real_statistics['rises'])
                avg_volatility = np.mean(self.real_statistics['volatilities'])

                # 确保起始点在合理范围内
                real_starts = [seq[0] for seq in self.real_sequences]
                start_range = [np.min(real_starts), np.max(real_starts)]

                # 将起始温度映射到控制点范围
                target_start_temp = np.random.uniform(start_range[0], start_range[1])
                start_control_point = 2 * (target_start_temp - self.min_temp) / (self.max_temp - self.min_temp) - 1
                start_control_point = np.clip(start_control_point, -1, 1)

                # 生成渐进上升的控制点序列
                end_control_point = np.random.uniform(0.5, 1.0)  # 确保结束温度较高
                base_trend = np.linspace(start_control_point, end_control_point, self.control_points)

                # 添加适度的随机变化，但保持整体趋势
                noise_strength = min(0.1, avg_volatility / 1000)
                noise = np.random.normal(0, noise_strength, self.control_points)

                # 应用噪声但确保单调性
                particle.position = base_trend + noise

                # 确保单调递增趋势
                for j in range(1, len(particle.position)):
                    if particle.position[j] < particle.position[j-1]:
                        particle.position[j] = particle.position[j-1] + np.random.uniform(0.01, 0.05)

                particle.position = np.clip(particle.position, -1, 1)
            else:
                # 默认策略：确保上升趋势
                start_point = np.random.uniform(-0.8, -0.2)
                end_point = np.random.uniform(0.2, 0.8)
                base_trend = np.linspace(start_point, end_point, self.control_points)
                noise = np.random.normal(0, 0.05, self.control_points)
                particle.position = base_trend + noise
                particle.position = np.clip(particle.position, -1, 1)

            self.swarm.append(particle)
        
        self.logger.info(f"粒子群初始化完成，总粒子数: {len(self.swarm)}")
    
    def control_points_to_sequence(self, control_points: np.ndarray) -> np.ndarray:
        """将控制点转换为温度序列（优化版）"""
        # 确定序列长度
        if self.enable_variable_length:
            # 基于控制点特征动态确定长度
            complexity = np.std(control_points)
            length_factor = np.clip(complexity, 0.1, 1.0)
            target_length = int(self.min_length +
                              (self.max_length - self.min_length) * length_factor)
        else:
            target_length = self.sequence_length

        # 使用改进的非线性映射策略
        from scipy.interpolate import CubicSpline

        x_control = np.linspace(0, target_length - 1, len(control_points))
        x_sequence = np.arange(target_length)

        # 改进的温度映射策略 - 确保起始温度合理
        temp_control = self._improved_temperature_mapping(control_points)

        # 使用约束的三次样条插值，减少振荡
        cs = CubicSpline(x_control, temp_control, bc_type='clamped')
        sequence = cs(x_sequence)

        # 应用平滑约束
        sequence = self._apply_smoothness_constraints(sequence)

        # 添加适度的真实感噪声（减少噪声强度）
        if self.real_statistics:
            avg_volatility = np.mean(self.real_statistics['volatilities'])
            noise_scale = avg_volatility * 0.05  # 降低到5%的真实波动性
            noise = np.random.normal(0, noise_scale, len(sequence))
            sequence += noise

        # 确保在合理范围内
        sequence = np.clip(sequence, self.min_temp, self.max_temp)

        return sequence

    def _improved_temperature_mapping(self, control_points: np.ndarray) -> np.ndarray:
        """改进的温度映射策略，确保起始温度合理"""
        # 对起始点应用特殊约束
        temp_control = np.zeros_like(control_points)

        # 起始温度约束 - 基于真实数据统计
        if self.real_statistics:
            # 使用真实数据的起始温度范围
            real_starts = [seq[0] for seq in self.real_sequences]
            start_temp_min = np.min(real_starts)
            start_temp_max = np.max(real_starts)
            start_temp_range = start_temp_max - start_temp_min

            # 起始点使用更严格的映射
            start_factor = (control_points[0] + 1) / 2  # [0, 1]
            temp_control[0] = start_temp_min + start_temp_range * start_factor
        else:
            # 默认起始温度范围 [16°C, 32°C]
            start_factor = (control_points[0] + 1) / 2
            temp_control[0] = 16.0 + 16.0 * start_factor

        # 中间点使用渐进式映射
        for i in range(1, len(control_points)):
            progress = i / (len(control_points) - 1)  # [0, 1]

            # 使用非线性映射，前期变化较慢，后期变化较快
            nonlinear_factor = progress ** 0.7  # 非线性因子

            # 基础温度映射
            base_temp = self.min_temp + (self.max_temp - self.min_temp) * (control_points[i] + 1) / 2

            # 应用渐进约束，确保温度上升趋势
            min_temp_at_progress = temp_control[0] + (self.max_temp - temp_control[0]) * nonlinear_factor * 0.3
            max_temp_at_progress = temp_control[0] + (self.max_temp - temp_control[0]) * (0.5 + nonlinear_factor * 0.5)

            temp_control[i] = np.clip(base_temp, min_temp_at_progress, max_temp_at_progress)

        # 确保整体上升趋势
        for i in range(1, len(temp_control)):
            if temp_control[i] < temp_control[i-1]:
                temp_control[i] = temp_control[i-1] + np.random.uniform(0.1, 2.0)

        return temp_control

    def _apply_smoothness_constraints(self, sequence: np.ndarray) -> np.ndarray:
        """应用平滑性约束，减少过度波动"""
        if len(sequence) < 3:
            return sequence

        # 使用移动平均平滑
        window_size = max(3, len(sequence) // 100)  # 动态窗口大小
        smoothed = np.convolve(sequence, np.ones(window_size)/window_size, mode='same')

        # 保持边界值
        smoothed[0] = sequence[0]
        smoothed[-1] = sequence[-1]

        # 限制最大变化率
        max_change = 0.5  # 每步最大变化0.5°C
        for i in range(1, len(smoothed)):
            change = smoothed[i] - smoothed[i-1]
            if abs(change) > max_change:
                smoothed[i] = smoothed[i-1] + np.sign(change) * max_change

        return smoothed

    def advanced_fitness_function(self, sequence: np.ndarray) -> Tuple[float, Dict]:
        """
        高级适应度函数
        
        Args:
            sequence: 温度序列
            
        Returns:
            适应度分数和详细分解
        """
        if len(sequence) == 0:
            return 0.0, {}
        
        fitness_components = {}
        
        # 1. 基础质量分数 (30%)
        temp_rise = sequence[-1] - sequence[0]
        if self.real_statistics:
            target_rise = np.mean(self.real_statistics['rises'])
            rise_score = 1.0 - abs(temp_rise - target_rise) / target_rise
            rise_score = max(0, rise_score)
        else:
            rise_score = min(temp_rise / 120.0, 1.0)
        
        fitness_components['rise_score'] = rise_score
        
        # 2. 统计相似性分数 (25%)
        similarity_score = 0.0
        if self.real_statistics:
            # 均值相似性
            seq_mean = np.mean(sequence)
            target_mean = np.mean(self.real_statistics['means'])
            mean_similarity = 1.0 - abs(seq_mean - target_mean) / target_mean
            
            # 标准差相似性
            seq_std = np.std(sequence)
            target_std = np.mean(self.real_statistics['stds'])
            std_similarity = 1.0 - abs(seq_std - target_std) / target_std
            
            # 波动性相似性
            seq_changes = np.diff(sequence)
            seq_volatility = np.std(seq_changes)
            target_volatility = np.mean(self.real_statistics['volatilities'])
            volatility_similarity = 1.0 - abs(seq_volatility - target_volatility) / target_volatility
            
            similarity_score = (mean_similarity + std_similarity + volatility_similarity) / 3
            similarity_score = max(0, similarity_score)
        
        fitness_components['similarity_score'] = similarity_score
        
        # 3. 趋势一致性分数 (20%)
        x = np.arange(len(sequence))
        slope = np.polyfit(x, sequence, 1)[0]
        
        if self.real_statistics:
            target_slope = np.mean(self.real_statistics['slopes'])
            trend_score = 1.0 - abs(slope - target_slope) / abs(target_slope)
            trend_score = max(0, trend_score)
        else:
            trend_score = 1.0 if slope > 0 else 0.0
        
        fitness_components['trend_score'] = trend_score
        
        # 4. 阶段模式匹配分数 (15%)
        stage_score = 0.0
        if self.real_statistics:
            # 分为5个阶段
            stage_size = len(sequence) // 5
            seq_stages = []
            for i in range(5):
                start_idx = i * stage_size
                end_idx = (i + 1) * stage_size if i < 4 else len(sequence)
                stage_mean = np.mean(sequence[start_idx:end_idx])
                seq_stages.append(stage_mean)
            
            # 与真实数据的阶段模式比较
            target_stages = np.mean(self.real_statistics['stage_patterns'], axis=0)
            stage_similarities = []
            for i in range(5):
                if target_stages[i] != 0:
                    sim = 1.0 - abs(seq_stages[i] - target_stages[i]) / abs(target_stages[i])
                    stage_similarities.append(max(0, sim))
            
            stage_score = np.mean(stage_similarities) if stage_similarities else 0.0
        
        fitness_components['stage_score'] = stage_score
        
        # 5. 增强的平滑性分数 (20%) - 避免过度波动
        changes = np.diff(sequence)

        # 基础平滑性
        if len(changes) > 0 and np.mean(np.abs(changes)) > 0:
            basic_smoothness = 1.0 / (1.0 + np.std(changes) / np.mean(np.abs(changes)))
        else:
            basic_smoothness = 1.0

        # 梯度平滑性 - 惩罚急剧变化
        if len(changes) > 1:
            gradient_changes = np.diff(changes)
            gradient_smoothness = 1.0 / (1.0 + np.std(gradient_changes) * 10)
        else:
            gradient_smoothness = 1.0

        # 局部波动惩罚
        local_volatility_penalty = 0.0
        if len(sequence) > 10:
            # 计算局部波动
            window_size = min(10, len(sequence) // 10)
            local_stds = []
            for i in range(0, len(sequence) - window_size, window_size):
                window = sequence[i:i + window_size]
                local_stds.append(np.std(window))

            if local_stds:
                avg_local_std = np.mean(local_stds)
                local_volatility_penalty = min(avg_local_std / 10.0, 0.5)  # 最大惩罚0.5

        # 综合平滑性分数
        smoothness = (basic_smoothness * 0.4 + gradient_smoothness * 0.6) * (1.0 - local_volatility_penalty)
        fitness_components['smoothness_score'] = smoothness

        # 6. 起始温度合理性分数 (5%) - 新增
        start_temp_score = 1.0
        if self.real_statistics:
            real_starts = [seq[0] for seq in self.real_sequences]
            start_temp_range = [np.min(real_starts), np.max(real_starts)]
            seq_start = sequence[0]

            if start_temp_range[0] <= seq_start <= start_temp_range[1]:
                start_temp_score = 1.0
            else:
                # 计算偏离程度
                if seq_start < start_temp_range[0]:
                    deviation = start_temp_range[0] - seq_start
                else:
                    deviation = seq_start - start_temp_range[1]

                range_width = start_temp_range[1] - start_temp_range[0]
                start_temp_score = max(0.0, 1.0 - deviation / range_width)

        fitness_components['start_temp_score'] = start_temp_score

        # 调整后的加权组合 - 提高平滑性权重
        weights = {
            'rise_score': 0.25,           # 降低
            'similarity_score': 0.20,     # 降低
            'trend_score': 0.15,          # 降低
            'stage_score': 0.15,          # 保持
            'smoothness_score': 0.20,     # 大幅提高
            'start_temp_score': 0.05      # 新增
        }
        
        total_fitness = sum(weights[key] * fitness_components[key] 
                           for key in weights.keys())
        
        fitness_components['total_fitness'] = total_fitness
        fitness_components['weights'] = weights
        
        return total_fitness, fitness_components

    def _calculate_swarm_diversity(self) -> float:
        """计算粒子群多样性"""
        if len(self.swarm) < 2:
            return 0.0

        positions = np.array([particle.position for particle in self.swarm])
        distances = []

        for i in range(len(positions)):
            for j in range(i + 1, len(positions)):
                dist = np.linalg.norm(positions[i] - positions[j])
                distances.append(dist)

        return np.mean(distances) if distances else 0.0

    def _maintain_diversity(self, iteration: int):
        """维持粒子群多样性"""
        diversity = self._calculate_swarm_diversity()
        self.diversity_history.append(diversity)

        if diversity < self.diversity_threshold:
            self.logger.info(f"迭代 {iteration}: 多样性过低 ({diversity:.4f})，执行多样性维持")

            # 对停滞的粒子进行变异
            for particle in self.swarm:
                if particle.stagnation_count > 10:  # 停滞超过10次迭代
                    if np.random.random() < self.mutation_probability:
                        # 变异操作
                        mutation = np.random.normal(0, self.mutation_strength, len(particle.position))
                        particle.position += mutation
                        particle.position = np.clip(particle.position, -1, 1)
                        particle.stagnation_count = 0
                        self.logger.debug(f"对停滞粒子执行变异")

    def _update_parameters(self, iteration: int):
        """改进的自适应参数更新策略"""
        progress = iteration / self.max_iterations

        # 改进的惯性权重策略 - 使用非线性衰减
        # 前期保持较高惯性，后期快速衰减
        if progress < 0.5:
            # 前半段缓慢衰减
            self.w = self.w_start - (self.w_start - 0.6) * (progress * 2) ** 0.5
        else:
            # 后半段快速衰减
            self.w = 0.6 - (0.6 - self.w_end) * ((progress - 0.5) * 2) ** 2

        # 改进的学习因子动态调整
        # c1 (个体学习): 前期高，后期低
        self.c1 = self.c1_start - (self.c1_start - self.c1_end) * (progress ** 0.8)
        # c2 (社会学习): 前期低，后期高，但增长更平缓
        self.c2 = self.c2_start + (self.c2_end - self.c2_start) * (progress ** 1.2)

        # 改进的变异概率调整策略
        if len(self.fitness_history) > 20:
            recent_improvement = (self.fitness_history[-1] - self.fitness_history[-20]) / 20

            # 计算收敛速度
            if len(self.fitness_history) > 50:
                long_term_improvement = (self.fitness_history[-1] - self.fitness_history[-50]) / 50
                convergence_rate = recent_improvement / max(long_term_improvement, 1e-8)
            else:
                convergence_rate = 1.0

            # 根据收敛情况和多样性调整变异概率
            if recent_improvement < 0.0005:  # 改进很小
                self.mutation_probability = min(0.4, self.mutation_probability * 1.15)
            elif recent_improvement > 0.005:  # 改进较大
                self.mutation_probability = max(0.03, self.mutation_probability * 0.85)

            # 根据迭代进度调整基础变异概率
            base_mutation = 0.1 * (1 - progress) + 0.05 * progress
            self.mutation_probability = max(base_mutation, self.mutation_probability)

    def _update_particle(self, particle: 'Particle', iteration: int):
        """更新单个粒子"""
        # 速度更新
        r1, r2 = np.random.random(2)

        cognitive_component = self.c1 * r1 * (particle.best_position - particle.position)
        social_component = self.c2 * r2 * (self.global_best_position - particle.position)

        particle.velocity = (self.w * particle.velocity +
                           cognitive_component +
                           social_component)

        # 速度限制
        max_velocity = 0.2
        particle.velocity = np.clip(particle.velocity, -max_velocity, max_velocity)

        # 位置更新
        particle.position += particle.velocity
        particle.position = np.clip(particle.position, -1, 1)

        # 应用约束
        if self.constraints:
            sequence = self.control_points_to_sequence(particle.position)
            constrained_sequence = self.constraints.apply_all_constraints(sequence)

            # 如果约束后序列发生显著变化，反向更新控制点
            if np.mean(np.abs(constrained_sequence - sequence)) > 1.0:
                # 简单的反向映射 - 重采样约束后的序列到控制点数量
                step = len(constrained_sequence) // len(particle.position)
                if step > 0:
                    sampled_temps = constrained_sequence[::step][:len(particle.position)]
                    # 如果采样不够，用最后一个值填充
                    if len(sampled_temps) < len(particle.position):
                        sampled_temps = np.pad(sampled_temps, (0, len(particle.position) - len(sampled_temps)),
                                             mode='constant', constant_values=sampled_temps[-1])

                    particle.position = (sampled_temps - self.min_temp) / (self.max_temp - self.min_temp) * 2 - 1
                    particle.position = np.clip(particle.position, -1, 1)

    def optimize_advanced(self, custom_fitness_function: Optional[Callable] = None) -> Dict:
        """
        执行高级PSO优化

        Args:
            custom_fitness_function: 自定义适应度函数

        Returns:
            优化结果字典
        """
        self.logger.info("开始高级PSO优化...")

        # 首先加载真实数据统计信息
        self._load_real_data_statistics()

        # 然后初始化组件（包括混合适应度评估器）
        self._initialize_components()

        # 如果启用了混合适应度评估且有真实数据，更新参考序列
        if (self.enable_hybrid_fitness and self.hybrid_fitness_evaluator and
            hasattr(self, 'real_sequences') and self.real_sequences):
            try:
                self.hybrid_fitness_evaluator.set_reference_sequences(self.real_sequences)
                self.logger.info(f"混合适应度评估器已更新参考序列，数量: {len(self.real_sequences)}")
            except Exception as e:
                self.logger.warning(f"更新混合适应度评估器参考序列失败: {e}")

        # 初始化粒子群
        self._initialize_swarm()

        # 选择适应度函数
        if custom_fitness_function:
            fitness_function = custom_fitness_function
        elif self.enable_hybrid_fitness and self.hybrid_fitness_evaluator:
            fitness_function = self.hybrid_fitness_evaluator
            self.logger.info("使用混合适应度评估器")
        else:
            fitness_function = self.advanced_fitness_function
            self.logger.info("使用纯统计学适应度函数")

        # 初始适应度评估
        self.logger.info("初始适应度评估...")
        for particle in self.swarm:
            sequence = self.control_points_to_sequence(particle.position)

            if custom_fitness_function:
                particle.fitness = fitness_function(sequence)
            elif self.enable_hybrid_fitness and self.hybrid_fitness_evaluator:
                # 混合适应度评估器返回单个值
                particle.fitness = fitness_function(sequence)
            else:
                # 统计学适应度函数返回元组
                particle.fitness, _ = fitness_function(sequence)

            particle.update_best()

            # 更新全局最佳
            if particle.fitness > self.global_best_fitness:
                self.global_best_fitness = particle.fitness
                self.global_best_position = particle.position.copy()

        self.fitness_history.append(self.global_best_fitness)
        self.logger.info(f"初始最佳适应度: {self.global_best_fitness:.6f}")

        # 主优化循环
        for iteration in range(self.max_iterations):
            # 更新参数
            self._update_parameters(iteration)

            # 更新粒子
            for particle in self.swarm:
                self._update_particle(particle, iteration)

                # 评估适应度
                sequence = self.control_points_to_sequence(particle.position)

                if custom_fitness_function:
                    particle.fitness = fitness_function(sequence)
                elif self.enable_hybrid_fitness and self.hybrid_fitness_evaluator:
                    # 混合适应度评估器返回单个值
                    particle.fitness = fitness_function(sequence)
                else:
                    # 统计学适应度函数返回元组
                    particle.fitness, _ = fitness_function(sequence)

                particle.update_best()

                # 更新全局最佳
                if particle.fitness > self.global_best_fitness:
                    self.global_best_fitness = particle.fitness
                    self.global_best_position = particle.position.copy()
                    self.logger.info(f"迭代 {iteration}: 新的最佳适应度 {self.global_best_fitness:.6f}")

            # 维持多样性
            self._maintain_diversity(iteration)

            # 记录历史
            self.fitness_history.append(self.global_best_fitness)

            # 进度报告
            if (iteration + 1) % 20 == 0:
                diversity = self._calculate_swarm_diversity()
                self.logger.info(f"迭代 {iteration + 1}/{self.max_iterations}: "
                               f"最佳适应度={self.global_best_fitness:.6f}, "
                               f"多样性={diversity:.4f}, "
                               f"变异概率={self.mutation_probability:.3f}")

            # 改进的早停检查
            if len(self.fitness_history) > 30:
                # 短期改进检查
                short_term_improvement = (self.fitness_history[-1] - self.fitness_history[-10]) / 10

                # 长期改进检查
                if len(self.fitness_history) > 50:
                    long_term_improvement = (self.fitness_history[-1] - self.fitness_history[-50]) / 50

                    # 多重收敛条件
                    conditions = [
                        short_term_improvement < 1e-6,  # 短期无改进
                        long_term_improvement < 5e-6,   # 长期改进很小
                        self._calculate_swarm_diversity() < 0.05,  # 多样性过低
                        self.global_best_fitness > 0.95  # 适应度已经很高
                    ]

                    # 满足多个条件才早停
                    if sum(conditions) >= 2:
                        self.logger.info(f"迭代 {iteration}: 满足多重收敛条件，提前停止")
                        self.logger.info(f"  短期改进: {short_term_improvement:.8f}")
                        self.logger.info(f"  长期改进: {long_term_improvement:.8f}")
                        self.logger.info(f"  多样性: {self._calculate_swarm_diversity():.6f}")
                        self.logger.info(f"  当前适应度: {self.global_best_fitness:.6f}")
                        break
                elif short_term_improvement < 5e-7:
                    self.logger.info(f"迭代 {iteration}: 短期无改进，提前停止")
                    break

        # 生成最终结果
        best_sequence = self.control_points_to_sequence(self.global_best_position)

        # 获取详细的适应度分解
        if custom_fitness_function:
            final_fitness = self.global_best_fitness
            fitness_breakdown = {}
        elif self.enable_hybrid_fitness and self.hybrid_fitness_evaluator:
            # 获取混合适应度评估的详细分解
            final_fitness, fitness_breakdown = self.hybrid_fitness_evaluator.evaluate_hybrid_fitness(best_sequence)
        else:
            # 使用纯统计学适应度函数
            final_fitness, fitness_breakdown = self.advanced_fitness_function(best_sequence)

        results = {
            'best_fitness': final_fitness,
            'best_sequence': best_sequence,
            'best_control_points': self.global_best_position,
            'fitness_history': self.fitness_history,
            'diversity_history': self.diversity_history,
            'total_iterations': iteration + 1,
            'converged': len(self.fitness_history) > 50 and recent_improvement < 1e-6,
            'fitness_breakdown': fitness_breakdown,
            'optimization_parameters': {
                'swarm_size': self.swarm_size,
                'control_points': self.control_points,
                'final_w': self.w,
                'final_c1': self.c1,
                'final_c2': self.c2,
                'final_mutation_prob': self.mutation_probability
            }
        }

        # 添加混合适应度评估器的性能统计
        if self.enable_hybrid_fitness and self.hybrid_fitness_evaluator:
            performance_stats = self.hybrid_fitness_evaluator.get_performance_stats()
            results['hybrid_fitness_stats'] = performance_stats

            # 记录性能统计到日志
            cache_stats = performance_stats.get('cache_stats', {})
            if cache_stats.get('hits', 0) + cache_stats.get('misses', 0) > 0:
                self.logger.info(f"混合适应度评估器缓存命中率: {cache_stats.get('hit_rate', 0):.1%}")

        self.logger.info(f"高级PSO优化完成！最佳适应度: {final_fitness:.6f}")
        return results

    def set_hybrid_fitness_weights(self, classifier_weight: float, statistical_weight: float):
        """
        设置混合适应度评估的权重

        Args:
            classifier_weight: 分类器权重
            statistical_weight: 统计学权重
        """
        if self.hybrid_fitness_evaluator:
            self.hybrid_fitness_evaluator.set_weights(classifier_weight, statistical_weight)
            self.logger.info(f"混合适应度权重已更新: 分类器={classifier_weight:.1%}, 统计学={statistical_weight:.1%}")
        else:
            self.logger.warning("混合适应度评估器未初始化，无法设置权重")

    def enable_hybrid_fitness_evaluation(self, enable: bool = True):
        """
        启用或禁用混合适应度评估

        Args:
            enable: 是否启用
        """
        self.enable_hybrid_fitness = enable
        if enable and not self.hybrid_fitness_evaluator:
            # 尝试重新初始化混合适应度评估器
            try:
                self.hybrid_fitness_evaluator = HybridFitnessEvaluator(
                    config_path=self.config_path,
                    model_dir=self.model_dir,
                    statistical_fitness_function=self.advanced_fitness_function,
                    reference_sequences=self.real_sequences if hasattr(self, 'real_sequences') and self.real_sequences else []
                )
                self.logger.info("混合适应度评估器已重新初始化")
            except Exception as e:
                self.logger.error(f"重新初始化混合适应度评估器失败: {e}")
                self.enable_hybrid_fitness = False

        self.logger.info(f"混合适应度评估: {'启用' if self.enable_hybrid_fitness else '禁用'}")

    def get_hybrid_fitness_stats(self) -> Dict:
        """
        获取混合适应度评估器的性能统计

        Returns:
            性能统计字典
        """
        if self.hybrid_fitness_evaluator:
            return self.hybrid_fitness_evaluator.get_performance_stats()
        else:
            return {'error': '混合适应度评估器未初始化'}

    def clear_hybrid_fitness_cache(self):
        """清空混合适应度评估器的缓存"""
        if self.hybrid_fitness_evaluator:
            self.hybrid_fitness_evaluator.clear_cache()
            self.logger.info("混合适应度评估器缓存已清空")
        else:
            self.logger.warning("混合适应度评估器未初始化，无法清空缓存")

    def save_results(self, results: Dict, output_dir: str = "results") -> str:
        """
        保存优化结果

        Args:
            results: 优化结果
            output_dir: 输出目录

        Returns:
            保存的文件路径
        """
        os.makedirs(output_dir, exist_ok=True)

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # 保存温度序列为CSV
        sequence = results['best_sequence']
        time_points = np.arange(len(sequence))
        time_minutes = time_points * 0.1  # 假设每个点0.1分钟

        df = pd.DataFrame({
            '时间点': time_points,
            '温度(°C)': sequence,
            '时间(分钟)': time_minutes,
            '适应度': [results['best_fitness']] * len(sequence),
            '迭代次数': [results['total_iterations']] * len(sequence),
            '是否收敛': [results['converged']] * len(sequence)
        })

        csv_path = os.path.join(output_dir, f"advanced_temperature_sequence_{timestamp}.csv")
        df.to_csv(csv_path, index=False, encoding='utf-8-sig')

        # 保存详细结果为JSON
        import json

        # 准备JSON数据（处理numpy数组）
        json_results = {
            'best_fitness': float(results['best_fitness']),
            'total_iterations': int(results['total_iterations']),
            'converged': bool(results['converged']),
            'sequence_length': len(sequence),
            'temperature_range': {
                'min': float(np.min(sequence)),
                'max': float(np.max(sequence)),
                'rise': float(sequence[-1] - sequence[0])
            },
            'fitness_breakdown': {k: float(v) if isinstance(v, (int, float, np.number)) else v
                                for k, v in results.get('fitness_breakdown', {}).items()},
            'optimization_parameters': results['optimization_parameters'],
            'fitness_history': [float(f) for f in results['fitness_history']],
            'diversity_history': [float(d) for d in results['diversity_history']],
            'timestamp': timestamp
        }

        json_path = os.path.join(output_dir, f"advanced_pso_results_{timestamp}.json")
        with open(json_path, 'w', encoding='utf-8') as f:
            json.dump(json_results, f, indent=2, ensure_ascii=False)

        self.logger.info(f"结果已保存:")
        self.logger.info(f"  CSV文件: {csv_path}")
        self.logger.info(f"  JSON文件: {json_path}")

        return csv_path

    def generate_optimization_report(self, results: Dict) -> str:
        """
        生成优化报告

        Args:
            results: 优化结果

        Returns:
            报告文本
        """
        sequence = results['best_sequence']

        report = f"""
{'='*80}
高级PSO温度序列优化报告
{'='*80}

优化配置:
  粒子群大小: {self.swarm_size}
  最大迭代次数: {self.max_iterations}
  控制点数量: {self.control_points}
  序列长度: {len(sequence):,}

优化结果:
  最佳适应度: {results['best_fitness']:.6f}
  实际迭代次数: {results['total_iterations']}
  是否收敛: {'是' if results['converged'] else '否'}

温度序列特征:
  起始温度: {sequence[0]:.2f}°C
  结束温度: {sequence[-1]:.2f}°C
  温度上升: {sequence[-1] - sequence[0]:.2f}°C
  平均温度: {np.mean(sequence):.2f}°C
  温度标准差: {np.std(sequence):.2f}°C
  最低温度: {np.min(sequence):.2f}°C
  最高温度: {np.max(sequence):.2f}°C

变化特征:
  平均变化率: {np.mean(np.diff(sequence)):.6f}°C/点
  最大上升率: {np.max(np.diff(sequence)):.6f}°C/点
  最大下降率: {np.min(np.diff(sequence)):.6f}°C/点
  变化率标准差: {np.std(np.diff(sequence)):.6f}°C/点

趋势分析:
  线性斜率: {np.polyfit(np.arange(len(sequence)), sequence, 1)[0]:.8f}°C/点
  整体趋势: {'上升' if np.polyfit(np.arange(len(sequence)), sequence, 1)[0] > 0 else '下降'}
"""

        # 添加适应度分解信息
        if 'fitness_breakdown' in results and results['fitness_breakdown']:
            report += f"\n适应度分解:\n"
            breakdown = results['fitness_breakdown']
            if 'weights' in breakdown:
                weights = breakdown['weights']
                for component, weight in weights.items():
                    if component in breakdown:
                        score = breakdown[component]
                        weighted_score = score * weight
                        report += f"  {component}: {score:.4f} (权重: {weight:.2f}, 加权: {weighted_score:.4f})\n"

        # 添加优化参数信息
        if 'optimization_parameters' in results:
            params = results['optimization_parameters']
            report += f"\n最终优化参数:\n"
            for param, value in params.items():
                report += f"  {param}: {value}\n"

        # 添加与真实数据的对比
        if self.real_statistics:
            report += f"\n与真实数据对比:\n"
            opt_rise = sequence[-1] - sequence[0]
            real_rises = self.real_statistics['rises']
            real_rise_range = [np.min(real_rises), np.max(real_rises)]

            report += f"  温度上升对比:\n"
            report += f"    优化结果: {opt_rise:.2f}°C\n"
            report += f"    真实范围: {real_rise_range[0]:.2f} - {real_rise_range[1]:.2f}°C\n"
            report += f"    是否在范围内: {'是' if real_rise_range[0] <= opt_rise <= real_rise_range[1] else '否'}\n"

            opt_mean = np.mean(sequence)
            real_means = self.real_statistics['means']
            real_mean_avg = np.mean(real_means)

            report += f"  平均温度对比:\n"
            report += f"    优化结果: {opt_mean:.2f}°C\n"
            report += f"    真实平均: {real_mean_avg:.2f}°C\n"
            report += f"    差异: {abs(opt_mean - real_mean_avg):.2f}°C\n"

        report += f"\n{'='*80}\n"

        return report

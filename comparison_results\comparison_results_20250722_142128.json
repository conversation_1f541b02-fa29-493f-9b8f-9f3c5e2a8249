{"timestamp": "2025-07-22T14:21:23.995684", "fitness_comparison": {"baseline_fitness": 0.85, "enhanced_fitness": 0.92, "absolute_improvement": 0.07000000000000006, "percentage_improvement": 8.235294117647067, "is_improved": true}, "convergence_comparison": {"baseline_iterations": 150, "enhanced_iterations": 120, "baseline_converged": false, "enhanced_converged": true, "baseline_final_diversity": 0.5, "enhanced_final_diversity": 0.3}, "sequence_comparison": {"baseline_length": 1000, "enhanced_length": 1200, "baseline_range": [20.0, 140.0], "enhanced_range": [16.839256545887594, 149.04485670394791], "baseline_temp_rise": 120.0, "enhanced_temp_rise": 129.97936764762088}, "statistical_comparison": {"mean": {"baseline": 80.0, "enhanced": 82.99398963649267, "difference": 2.993989636492671}, "std": {"baseline": 34.67567450537051, "enhanced": 37.547964479651604, "difference": 2.8722899742810952}, "skewness": {"baseline": 8.93481096387093e-17, "enhanced": 0.002239728646105025, "difference": 0.0022397286461049355}, "kurtosis": {"baseline": -1.2000024000023999, "enhanced": -1.1969886013651463, "difference": 0.003013798637253595}}, "trend_comparison": {"slope": {"baseline": 0.12012012012012012, "enhanced": 0.10835324782490866, "difference": -0.011766872295211464}, "r_squared": {"baseline": 1.0, "enhanced": 0.999291089486859, "difference": -0.0007089105131410367}, "monotonicity": {"baseline": 1.0, "enhanced": 0.5471226021684737}}, "improvement_summary": {"overall_improvement": true, "key_improvements": ["适应度提升 8.24%", "实现了收敛"], "areas_of_concern": [], "recommendation": "增强PSO算法显示出明显改进，建议采用"}}
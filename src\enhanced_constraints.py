#!/usr/bin/env python3
"""
增强约束系统模块

该模块负责：
1. 实现多层次约束体系
2. 统计特征约束
3. 温度变化模式约束
4. 阶段性特征约束
5. 业务趋势约束
"""

import numpy as np
from typing import Dict, List, Tuple, Optional
import logging
import yaml
from scipy import stats

logger = logging.getLogger(__name__)


class EnhancedConstraints:
    """增强约束系统"""
    
    def __init__(self, config_path: str = "config/config.yaml", 
                 business_analysis_results: Dict = None):
        """
        初始化增强约束系统
        
        Args:
            config_path: 配置文件路径
            business_analysis_results: 业务数据分析结果
        """
        with open(config_path, 'r', encoding='utf-8') as f:
            self.config = yaml.safe_load(f)
        
        self.temp_config = self.config['pso']['temperature_sequence']
        self.variable_length_config = self.temp_config.get('variable_length', {})
        self.variable_length_enabled = self.variable_length_config.get('enable', False)
        
        # 基础约束参数
        self.min_temp = self.temp_config['min_temperature']
        self.max_temp = self.temp_config['max_temperature']
        self.max_change_rate = self.temp_config['max_change_rate']
        self.sequence_length = self.temp_config['sequence_length']
        self.control_points = self.temp_config['control_points']
        
        if self.variable_length_enabled:
            self.min_length = self.variable_length_config['min_length']
            self.max_length = self.variable_length_config['max_length']
            self.default_length = self.variable_length_config['default_length']
        
        # 业务分析结果
        self.business_analysis = business_analysis_results or {}
        
        # 约束参数
        self.constraint_params = self._initialize_constraint_parameters()
        
        # 约束权重
        self.constraint_weights = {
            'basic_bounds': 1.0,
            'statistical_features': 0.8,
            'trend_consistency': 0.9,
            'stage_characteristics': 0.7,
            'change_patterns': 0.6,
            'business_trend': 1.0
        }
        
        logger.info("增强约束系统初始化完成")
    
    def _initialize_constraint_parameters(self) -> Dict:
        """
        初始化约束参数
        
        Returns:
            约束参数字典
        """
        params = {
            'temperature_bounds': {
                'min_temp': self.min_temp,
                'max_temp': self.max_temp
            },
            'change_rate_limits': {
                'max_change_rate': self.max_change_rate,
                'initial_max_change': 0.1
            }
        }
        
        # 从业务分析结果中提取约束参数
        if self.business_analysis:
            if 'constraint_parameters' in self.business_analysis:
                constraint_data = self.business_analysis['constraint_parameters']
                params.update(constraint_data)
            
            # 统计特征约束
            if 'basic_statistics' in self.business_analysis:
                stats_data = self.business_analysis['basic_statistics']['global_stats']
                params['statistical_constraints'] = {
                    'mean_temp_range': [
                        stats_data['global_mean'] - 2 * stats_data['global_std'],
                        stats_data['global_mean'] + 2 * stats_data['global_std']
                    ],
                    'std_temp_range': [
                        stats_data['global_std'] * 0.5,
                        stats_data['global_std'] * 1.5
                    ]
                }
            
            # 趋势约束
            if 'temperature_trends' in self.business_analysis:
                trend_data = self.business_analysis['temperature_trends']['global_trend_stats']
                params['trend_constraints'] = {
                    'expected_temp_rise_range': [
                        trend_data['temp_rise_min'],
                        trend_data['temp_rise_max']
                    ],
                    'start_temp_range': [
                        trend_data['start_temp_min'],
                        trend_data['start_temp_max']
                    ],
                    'end_temp_range': [
                        trend_data['end_temp_min'],
                        trend_data['end_temp_max']
                    ]
                }
            
            # 阶段约束
            if 'stage_characteristics' in self.business_analysis:
                stage_data = self.business_analysis['stage_characteristics']['global_stage_stats']
                params['stage_constraints'] = {
                    'early_stage_range': [
                        stage_data.get('early_global_min', self.min_temp),
                        stage_data.get('early_global_max', self.max_temp)
                    ],
                    'middle_stage_range': [
                        stage_data.get('middle_global_min', self.min_temp),
                        stage_data.get('middle_global_max', self.max_temp)
                    ],
                    'late_stage_range': [
                        stage_data.get('late_global_min', self.min_temp),
                        stage_data.get('late_global_max', self.max_temp)
                    ]
                }
            
            # 模式约束
            if 'change_patterns' in self.business_analysis:
                pattern_data = self.business_analysis['change_patterns']['global_pattern_stats']
                params['pattern_constraints'] = {
                    'max_volatility': pattern_data['volatility_max'],
                    'min_smoothness': pattern_data['smoothness_min'],
                    'max_acceleration': pattern_data['acceleration_max']
                }
        
        return params
    
    def apply_basic_bounds_constraint(self, sequence: np.ndarray) -> np.ndarray:
        """
        应用基础边界约束
        
        Args:
            sequence: 温度序列
            
        Returns:
            约束后的序列
        """
        return np.clip(sequence, self.min_temp, self.max_temp)
    
    def apply_statistical_constraints(self, sequence: np.ndarray) -> np.ndarray:
        """
        应用统计特征约束
        
        Args:
            sequence: 温度序列
            
        Returns:
            约束后的序列
        """
        if 'statistical_constraints' not in self.constraint_params:
            return sequence
        
        stat_constraints = self.constraint_params['statistical_constraints']
        constrained_sequence = sequence.copy()
        
        # 均值约束
        current_mean = np.mean(constrained_sequence)
        mean_range = stat_constraints['mean_temp_range']
        
        if current_mean < mean_range[0]:
            # 整体提升温度
            adjustment = mean_range[0] - current_mean
            constrained_sequence += adjustment
        elif current_mean > mean_range[1]:
            # 整体降低温度
            adjustment = current_mean - mean_range[1]
            constrained_sequence -= adjustment
        
        # 重新应用边界约束
        constrained_sequence = self.apply_basic_bounds_constraint(constrained_sequence)
        
        return constrained_sequence
    
    def apply_trend_constraints(self, sequence: np.ndarray) -> np.ndarray:
        """
        应用趋势约束
        
        Args:
            sequence: 温度序列
            
        Returns:
            约束后的序列
        """
        if 'trend_constraints' not in self.constraint_params:
            return sequence
        
        trend_constraints = self.constraint_params['trend_constraints']
        constrained_sequence = sequence.copy()
        
        # 起始温度约束
        start_range = trend_constraints['start_temp_range']
        if constrained_sequence[0] < start_range[0]:
            constrained_sequence[0] = start_range[0]
        elif constrained_sequence[0] > start_range[1]:
            constrained_sequence[0] = start_range[1]
        
        # 结束温度约束
        end_range = trend_constraints['end_temp_range']
        if constrained_sequence[-1] < end_range[0]:
            constrained_sequence[-1] = end_range[0]
        elif constrained_sequence[-1] > end_range[1]:
            constrained_sequence[-1] = end_range[1]
        
        # 温度上升幅度约束
        temp_rise = constrained_sequence[-1] - constrained_sequence[0]
        rise_range = trend_constraints['expected_temp_rise_range']
        
        if temp_rise < rise_range[0]:
            # 增加结束温度
            target_end_temp = constrained_sequence[0] + rise_range[0]
            target_end_temp = min(target_end_temp, end_range[1])
            constrained_sequence[-1] = target_end_temp
        elif temp_rise > rise_range[1]:
            # 减少结束温度或增加起始温度
            target_end_temp = constrained_sequence[0] + rise_range[1]
            target_end_temp = max(target_end_temp, end_range[0])
            constrained_sequence[-1] = target_end_temp
        
        return constrained_sequence
    
    def apply_stage_constraints(self, sequence: np.ndarray) -> np.ndarray:
        """
        应用阶段性约束
        
        Args:
            sequence: 温度序列
            
        Returns:
            约束后的序列
        """
        if 'stage_constraints' not in self.constraint_params:
            return sequence
        
        stage_constraints = self.constraint_params['stage_constraints']
        constrained_sequence = sequence.copy()
        
        # 定义阶段边界
        seq_len = len(constrained_sequence)
        early_end = int(seq_len * 0.2)
        middle_end = int(seq_len * 0.8)
        
        # 起始阶段约束
        early_range = stage_constraints['early_stage_range']
        constrained_sequence[:early_end] = np.clip(
            constrained_sequence[:early_end], early_range[0], early_range[1]
        )
        
        # 中期阶段约束
        middle_range = stage_constraints['middle_stage_range']
        constrained_sequence[early_end:middle_end] = np.clip(
            constrained_sequence[early_end:middle_end], middle_range[0], middle_range[1]
        )
        
        # 结束阶段约束
        late_range = stage_constraints['late_stage_range']
        constrained_sequence[middle_end:] = np.clip(
            constrained_sequence[middle_end:], late_range[0], late_range[1]
        )
        
        return constrained_sequence

    def apply_change_pattern_constraints(self, sequence: np.ndarray) -> np.ndarray:
        """
        应用变化模式约束

        Args:
            sequence: 温度序列

        Returns:
            约束后的序列
        """
        if 'pattern_constraints' not in self.constraint_params:
            return sequence

        pattern_constraints = self.constraint_params['pattern_constraints']
        constrained_sequence = sequence.copy()

        # 变化率约束
        for i in range(1, len(constrained_sequence)):
            change = constrained_sequence[i] - constrained_sequence[i-1]

            # 应用最大变化率限制
            if abs(change) > self.max_change_rate:
                sign = 1 if change > 0 else -1
                constrained_sequence[i] = constrained_sequence[i-1] + sign * self.max_change_rate

        # 波动性约束
        diff1 = np.diff(constrained_sequence)
        current_volatility = np.std(diff1)
        max_volatility = pattern_constraints.get('max_volatility', float('inf'))

        if current_volatility > max_volatility:
            # 平滑序列以减少波动性
            smoothing_factor = max_volatility / current_volatility
            for i in range(1, len(constrained_sequence) - 1):
                # 应用简单的平滑
                smoothed_value = (constrained_sequence[i-1] + constrained_sequence[i] + constrained_sequence[i+1]) / 3
                constrained_sequence[i] = constrained_sequence[i] * smoothing_factor + smoothed_value * (1 - smoothing_factor)

        return constrained_sequence

    def apply_business_trend_constraint(self, sequence: np.ndarray) -> np.ndarray:
        """
        应用业务趋势约束（确保整体上升趋势）

        Args:
            sequence: 温度序列

        Returns:
            约束后的序列
        """
        constrained_sequence = sequence.copy()

        # 确保整体上升趋势
        start_temp = constrained_sequence[0]
        end_temp = constrained_sequence[-1]

        # 如果不是上升趋势，强制调整
        if end_temp <= start_temp:
            # 计算目标上升幅度
            if 'trend_constraints' in self.constraint_params:
                rise_range = self.constraint_params['trend_constraints']['expected_temp_rise_range']
                target_rise = np.random.uniform(rise_range[0], rise_range[1])
            else:
                target_rise = 100.0  # 默认上升100°C

            # 调整结束温度
            target_end_temp = start_temp + target_rise
            target_end_temp = min(target_end_temp, self.max_temp)

            # 线性插值调整整个序列
            adjustment_factor = (target_end_temp - start_temp) / (len(constrained_sequence) - 1)
            for i in range(len(constrained_sequence)):
                constrained_sequence[i] = start_temp + i * adjustment_factor

        # 确保单调性（允许小幅波动但整体上升）
        for i in range(1, len(constrained_sequence)):
            min_allowed = constrained_sequence[i-1] - 2.0  # 允许小幅下降
            constrained_sequence[i] = max(constrained_sequence[i], min_allowed)

        return constrained_sequence

    def apply_all_constraints(self, sequence: np.ndarray,
                            constraint_types: List[str] = None) -> np.ndarray:
        """
        应用所有约束

        Args:
            sequence: 温度序列
            constraint_types: 要应用的约束类型列表，None表示应用所有约束

        Returns:
            约束后的序列
        """
        if constraint_types is None:
            constraint_types = [
                'basic_bounds',
                'business_trend',
                'trend_constraints',
                'stage_constraints',
                'change_patterns',
                'statistical_features'
            ]

        constrained_sequence = sequence.copy()

        # 按优先级顺序应用约束
        for constraint_type in constraint_types:
            if constraint_type == 'basic_bounds':
                constrained_sequence = self.apply_basic_bounds_constraint(constrained_sequence)
            elif constraint_type == 'business_trend':
                constrained_sequence = self.apply_business_trend_constraint(constrained_sequence)
            elif constraint_type == 'trend_constraints':
                constrained_sequence = self.apply_trend_constraints(constrained_sequence)
            elif constraint_type == 'stage_constraints':
                constrained_sequence = self.apply_stage_constraints(constrained_sequence)
            elif constraint_type == 'change_patterns':
                constrained_sequence = self.apply_change_pattern_constraints(constrained_sequence)
            elif constraint_type == 'statistical_features':
                constrained_sequence = self.apply_statistical_constraints(constrained_sequence)

        # 最终边界检查
        constrained_sequence = self.apply_basic_bounds_constraint(constrained_sequence)

        return constrained_sequence

    def validate_sequence_constraints(self, sequence: np.ndarray) -> Dict:
        """
        验证序列是否满足约束条件

        Args:
            sequence: 温度序列

        Returns:
            验证结果字典
        """
        validation_results = {
            'valid': True,
            'violations': [],
            'constraint_scores': {}
        }

        # 基础边界检查
        if np.any(sequence < self.min_temp) or np.any(sequence > self.max_temp):
            validation_results['valid'] = False
            validation_results['violations'].append('temperature_bounds')

        # 趋势检查
        temp_rise = sequence[-1] - sequence[0]
        if temp_rise <= 0:
            validation_results['valid'] = False
            validation_results['violations'].append('upward_trend')

        # 变化率检查
        diff = np.diff(sequence)
        max_change = np.max(np.abs(diff))
        if max_change > self.max_change_rate * 2:  # 允许一定容差
            validation_results['valid'] = False
            validation_results['violations'].append('change_rate')

        # 计算约束满足度分数
        validation_results['constraint_scores'] = {
            'bounds_score': self._calculate_bounds_score(sequence),
            'trend_score': self._calculate_trend_score(sequence),
            'smoothness_score': self._calculate_smoothness_score(sequence)
        }

        return validation_results

    def _calculate_bounds_score(self, sequence: np.ndarray) -> float:
        """计算边界约束满足度分数"""
        violations = np.sum((sequence < self.min_temp) | (sequence > self.max_temp))
        return max(0.0, 1.0 - violations / len(sequence))

    def _calculate_trend_score(self, sequence: np.ndarray) -> float:
        """计算趋势约束满足度分数"""
        temp_rise = sequence[-1] - sequence[0]
        if temp_rise <= 0:
            return 0.0

        # 基于期望上升幅度计算分数
        if 'trend_constraints' in self.constraint_params:
            rise_range = self.constraint_params['trend_constraints']['expected_temp_rise_range']
            if rise_range[0] <= temp_rise <= rise_range[1]:
                return 1.0
            else:
                # 计算偏离程度
                if temp_rise < rise_range[0]:
                    deviation = (rise_range[0] - temp_rise) / rise_range[0]
                else:
                    deviation = (temp_rise - rise_range[1]) / rise_range[1]
                return max(0.0, 1.0 - deviation)
        else:
            return 1.0 if temp_rise > 0 else 0.0

    def _calculate_smoothness_score(self, sequence: np.ndarray) -> float:
        """计算平滑性分数"""
        diff1 = np.diff(sequence)
        volatility = np.std(diff1)

        # 基于期望波动性计算分数
        if 'pattern_constraints' in self.constraint_params:
            max_volatility = self.constraint_params['pattern_constraints']['max_volatility']
            return max(0.0, 1.0 - volatility / max_volatility)
        else:
            # 使用默认评估
            return 1.0 / (1.0 + volatility)

    def get_constraint_summary(self) -> Dict:
        """
        获取约束系统摘要

        Returns:
            约束摘要字典
        """
        summary = {
            'constraint_types': list(self.constraint_weights.keys()),
            'constraint_weights': self.constraint_weights,
            'constraint_parameters': self.constraint_params,
            'variable_length_enabled': self.variable_length_enabled
        }

        if self.business_analysis:
            summary['business_analysis_available'] = True
            summary['analysis_components'] = list(self.business_analysis.keys())
        else:
            summary['business_analysis_available'] = False

        return summary


def main():
    """测试增强约束系统"""
    # 创建测试序列
    test_sequence = np.linspace(20, 150, 1000)

    # 添加一些噪声
    noise = np.random.normal(0, 5, len(test_sequence))
    test_sequence += noise

    print(f"原始序列: 长度={len(test_sequence)}, 范围=[{np.min(test_sequence):.2f}, {np.max(test_sequence):.2f}]")

    # 创建约束系统
    constraints = EnhancedConstraints()

    # 应用约束
    constrained_sequence = constraints.apply_all_constraints(test_sequence)

    print(f"约束后序列: 长度={len(constrained_sequence)}, 范围=[{np.min(constrained_sequence):.2f}, {np.max(constrained_sequence):.2f}]")

    # 验证约束
    validation = constraints.validate_sequence_constraints(constrained_sequence)
    print(f"约束验证: 有效={validation['valid']}")
    print(f"约束分数: {validation['constraint_scores']}")

    if validation['violations']:
        print(f"违反的约束: {validation['violations']}")

    # 获取约束摘要
    summary = constraints.get_constraint_summary()
    print(f"约束系统摘要: {len(summary['constraint_types'])} 种约束类型")

    print("增强约束系统测试完成！")


if __name__ == "__main__":
    main()

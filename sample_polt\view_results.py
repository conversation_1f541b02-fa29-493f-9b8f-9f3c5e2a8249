#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
查看温度曲线图结果的脚本
"""

import os
import subprocess
import sys
from pathlib import Path

def open_file(file_path):
    """
    使用系统默认程序打开文件
    """
    try:
        if sys.platform.startswith('win'):
            os.startfile(file_path)
        elif sys.platform.startswith('darwin'):  # macOS
            subprocess.run(['open', file_path])
        else:  # Linux
            subprocess.run(['xdg-open', file_path])
        return True
    except Exception as e:
        print(f"无法打开文件 {file_path}: {e}")
        return False

def main():
    """
    主函数 - 打开生成的图片和报告
    """
    current_dir = Path(__file__).parent
    
    print("温度序列数据分析结果查看器")
    print("=" * 50)
    
    # 要打开的文件列表
    files_to_open = [
        "all_samples_temperature_curves_overlay.png",  # 所有样本叠加图
        "temperature_statistics_summary.png",          # 统计摘要图
        "temperature_analysis_report.txt"              # 分析报告
    ]
    
    print("正在打开主要结果文件...")
    
    for filename in files_to_open:
        file_path = current_dir / filename
        if file_path.exists():
            print(f"打开: {filename}")
            open_file(str(file_path))
        else:
            print(f"文件不存在: {filename}")
    
    print("\n如果您想查看单个样本的温度曲线图，请手动打开以下文件:")
    
    # 列出所有单个样本的图片
    sample_files = list(current_dir.glob("sample_*_temperature_curve.png"))
    sample_files.sort(key=lambda x: int(x.stem.split('_')[1]))
    
    for i, file_path in enumerate(sample_files, 1):
        print(f"  {i:2d}. {file_path.name}")
    
    print(f"\n所有文件都保存在: {current_dir}")
    print("\n分析完成! 共生成了以下类型的图表:")
    print("  1. 21个单独样本的温度曲线图")
    print("  2. 所有样本的叠加温度曲线图")
    print("  3. 温度统计摘要图（包含平均温度、最高/最低温度、标准差、箱线图）")
    print("  4. 详细的数据分析报告")

if __name__ == "__main__":
    main()

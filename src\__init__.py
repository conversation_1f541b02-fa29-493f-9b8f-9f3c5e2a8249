#!/usr/bin/env python3
"""
化工车间温度序列PSO优化系统

基于粒子群优化(PSO)算法的温度序列优化系统，目标是找到能够最大化最终产品质量的最优温度序列。

主要模块：
- data_processor: 数据处理和成对比较数据集构建
- feature_extractor: 特征提取（统计特征 + LSTM时序特征）
- sequence_classifier: 序列比较分类器
- pso_optimizer: PSO优化算法实现
- fitness_evaluator: 适应度评估器
- sequence_generator: 温度序列生成器
- utils: 工具函数

作者: AI Assistant
版本: 1.0.0
"""

__version__ = "1.0.0"
__author__ = "AI Assistant"
__email__ = ""
__description__ = "基于PSO算法的化工车间温度序列优化系统"

# 导入主要类和函数
from .data_processor import DataProcessor
from .feature_extractor import FeatureExtractor
from .sequence_classifier import SequenceClassifier
from .pso_optimizer import PSOOptimizer
from .fitness_evaluator import FitnessEvaluator
from .sequence_generator import SequenceGenerator
from .utils import load_config, setup_logging, create_directories

__all__ = [
    "DataProcessor",
    "FeatureExtractor", 
    "SequenceClassifier",
    "PSOOptimizer",
    "FitnessEvaluator",
    "SequenceGenerator",
    "load_config",
    "setup_logging",
    "create_directories"
]

#!/usr/bin/env python3
"""
分类器训练主脚本

该脚本负责：
1. 加载和预处理数据
2. 构建成对比较数据集
3. 提取特征
4. 训练SVM分类器
5. 评估模型性能
6. 保存训练好的模型
"""

import os
import sys
import argparse
import logging
from datetime import datetime

# 添加src目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.data_processor import DataProcessor
from src.feature_extractor import FeatureExtractor
from src.sequence_classifier import SequenceClassifier
from src.utils import load_config, setup_logging, create_directories, print_system_info


def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='训练温度序列比较分类器')
    
    parser.add_argument('--config', type=str, default='config/config.yaml',
                       help='配置文件路径')
    parser.add_argument('--data-dir', type=str, default=None,
                       help='数据目录路径（覆盖配置文件设置）')
    parser.add_argument('--output-dir', type=str, default='models',
                       help='模型输出目录')
    parser.add_argument('--save-plots', action='store_true',
                       help='保存训练结果图表')
    parser.add_argument('--augment-data', action='store_true',
                       help='启用数据增强')
    parser.add_argument('--label1-weight', type=float, default=None,
                       help='label_1的权重（越低越好）')
    parser.add_argument('--verbose', action='store_true',
                       help='详细输出模式')
    
    return parser.parse_args()


def main():
    """主训练流程"""
    # 解析参数
    args = parse_arguments()

    # 打印系统信息
    print_system_info()

    # 初始化logger为None，以便在异常处理中检查
    logger = None

    try:
        # 加载配置
        config = load_config(args.config)

        # 设置日志
        logger = setup_logging(config)
        logger.info("开始训练温度序列比较分类器")
        
        # 创建输出目录
        create_directories([args.output_dir, 'results'])
        
        # 如果指定了数据目录，覆盖配置
        if args.data_dir:
            config['data']['data_dir'] = args.data_dir
        
        # 步骤1: 数据处理
        logger.info("=" * 50)
        logger.info("步骤1: 加载和处理数据")
        logger.info("=" * 50)
        
        data_processor = DataProcessor(args.config)

        # 设置权重参数（如果指定）
        if args.label1_weight is not None:
            # 使用命令行参数
            new_l1 = args.label1_weight

            # 验证和设置权重
            if new_l1 < 0:
                logger.error("权重必须为非负数")
                return False

            if new_l1 == 0:
                logger.error("权重必须大于0")
                return False

            data_processor.set_quality_weights(new_l1)
            logger.info(f"质量权重已设置: label_1={new_l1}")

        # 加载温度序列和标签
        temperature_sequences = data_processor.load_temperature_sequences()
        quality_labels = data_processor.load_quality_labels()

        # 创建成对比较数据集
        pairwise_data = data_processor.create_pairwise_dataset()
        
        # 数据增强（如果启用）
        if args.augment_data or config['classifier']['data_augmentation']['enable']:
            logger.info("启用数据增强...")
            augmentation_config = config['classifier']['data_augmentation']
            pairwise_data = data_processor.augment_pairwise_data(
                augmentation_factor=augmentation_config['augmentation_factor'],
                noise_level=augmentation_config['noise_level']
            )
        
        # 打印数据统计
        stats = data_processor.get_dataset_statistics()
        logger.info("数据集统计信息:")
        for key, value in stats.items():
            logger.info(f"  {key}: {value}")
        
        # 步骤2: 特征提取
        logger.info("=" * 50)
        logger.info("步骤2: 特征提取")
        logger.info("=" * 50)
        
        feature_extractor = FeatureExtractor(args.config)
        
        # 提取特征
        X, feature_names = feature_extractor.fit_transform(pairwise_data)
        y = [pair['label'] for pair in pairwise_data]
        
        logger.info(f"特征提取完成:")
        logger.info(f"  特征矩阵形状: {X.shape}")
        logger.info(f"  特征数量: {len(feature_names)}")
        logger.info(f"  样本数量: {len(y)}")
        logger.info(f"  标签分布: 类别0={y.count(0)}, 类别1={y.count(1)}")
        
        # 步骤3: 训练分类器
        logger.info("=" * 50)
        logger.info("步骤3: 训练分类器")
        logger.info("=" * 50)
        
        classifier = SequenceClassifier(args.config)
        
        # 训练模型
        training_results = classifier.train(X, y, feature_names)
        
        # 打印训练结果
        logger.info("训练结果:")
        logger.info(f"  训练准确率: {training_results['train_accuracy']:.4f}")
        logger.info(f"  测试准确率: {training_results['test_accuracy']:.4f}")
        logger.info(f"  交叉验证准确率: {training_results['cv_accuracy_mean']:.4f} ± {training_results['cv_accuracy_std']:.4f}")
        logger.info(f"  测试AUC: {training_results['test_auc']:.4f}")
        
        # 步骤4: 保存模型
        logger.info("=" * 50)
        logger.info("步骤4: 保存模型")
        logger.info("=" * 50)
        
        # 保存分类器
        classifier.save_model(args.output_dir)
        
        # 保存特征提取器
        import joblib
        feature_extractor_path = os.path.join(args.output_dir, 
                                            f"{config['model']['feature_extractor_name']}.joblib")
        joblib.dump(feature_extractor, feature_extractor_path)
        logger.info(f"特征提取器已保存到 {feature_extractor_path}")
        
        # 保存训练摘要
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        summary_path = os.path.join(args.output_dir, f"training_summary_{timestamp}.txt")
        
        with open(summary_path, 'w', encoding='utf-8') as f:
            f.write("温度序列比较分类器训练摘要\n")
            f.write("=" * 50 + "\n")
            f.write(f"训练时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"配置文件: {args.config}\n")
            f.write(f"数据目录: {config['data']['data_dir']}\n")
            f.write("\n数据统计:\n")
            for key, value in stats.items():
                f.write(f"  {key}: {value}\n")
            f.write(f"\n特征信息:\n")
            f.write(f"  特征数量: {len(feature_names)}\n")
            f.write(f"  样本数量: {len(y)}\n")
            f.write(f"  标签分布: 类别0={y.count(0)}, 类别1={y.count(1)}\n")
            f.write(f"\n训练结果:\n")
            for key, value in training_results.items():
                if key not in ['train_classification_report', 'test_classification_report', 'confusion_matrix']:
                    f.write(f"  {key}: {value}\n")
        
        logger.info(f"训练摘要已保存到 {summary_path}")
        
        # 步骤5: 生成图表（如果启用）
        if args.save_plots:
            logger.info("=" * 50)
            logger.info("步骤5: 生成训练结果图表")
            logger.info("=" * 50)
            
            plot_path = os.path.join('results', f"training_results_{timestamp}.png")
            classifier.plot_training_results(plot_path)
        
        # 步骤6: 模型验证
        logger.info("=" * 50)
        logger.info("步骤6: 模型验证")
        logger.info("=" * 50)
        
        # 使用部分数据进行验证
        validation_results = classifier.evaluate_model(X, y)
        logger.info("验证结果:")
        logger.info(f"  准确率: {validation_results['accuracy']:.4f}")
        logger.info(f"  AUC: {validation_results['auc']:.4f}")
        
        # 特征重要性分析
        feature_importance = classifier.get_feature_importance()
        if feature_importance:
            logger.info("特征重要性 (Top 10):")
            sorted_features = sorted(feature_importance.items(), key=lambda x: x[1], reverse=True)
            for i, (feature, importance) in enumerate(sorted_features[:10]):
                logger.info(f"  {i+1}. {feature}: {importance:.4f}")
        
        logger.info("=" * 50)
        logger.info("分类器训练完成！")
        logger.info("=" * 50)
        logger.info(f"模型文件保存在: {args.output_dir}")
        logger.info(f"训练摘要保存在: {summary_path}")
        if args.save_plots:
            logger.info(f"训练图表保存在: {plot_path}")
        
        return True
        
    except Exception as e:
        # 检查logger是否已初始化
        if logger is not None:
            logger.error(f"训练过程中发生错误: {e}")
            import traceback
            logger.error(traceback.format_exc())
        else:
            # 如果logger未初始化，使用print输出错误
            print(f"❌ 训练过程中发生错误: {e}")
            import traceback
            print("详细错误信息:")
            traceback.print_exc()
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

# 化工车间温度序列PSO优化系统

基于高级粒子群优化(PSO)算法的化工车间温度序列优化系统，通过数据驱动的智能优化找到最优温度控制序列。

## 🚀 快速开始

### 环境要求
- Python >= 3.8
- 主要依赖：numpy, pandas, scipy, scikit-learn, matplotlib

### 安装依赖
```bash
pip install -r requirements.txt
```

## 📊 使用方法

### 训练基于分类学习的适应度评估器
```bash
# 训练分类器（首次使用或需要重新训练时）
python main.py --mode train

# 完整流程（训练+优化）
python main.py --mode full
```

### 使用高级PSO进行优化（推荐）
```bash
# 使用预训练模型进行优化
python main.py --mode optimize --skip-training

# 自定义参数运行
python main.py --mode optimize --skip-training \
  --max-iterations 200 \
  --swarm-size 50 \
  --control-points 60
```

### 主要参数
- `--mode`: 执行模式（train/optimize/full）
- `--skip-training`: 跳过训练，使用现有模型
- `--max-iterations`: 最大迭代次数（默认200）
- `--swarm-size`: 粒子群大小（默认50）
- `--control-points`: 控制点数量（默认50）

## 📈 输出结果

运行完成后自动生成：
- **温度序列数据**: `results/advanced_temperature_sequence_*.csv`
- **优化报告**: `results/advanced_pso_report_*.txt`
- **对比图表**: `temperature_plots/Advanced_PSO_Comprehensive_Comparison.png`

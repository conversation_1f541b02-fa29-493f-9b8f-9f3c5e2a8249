#!/usr/bin/env python3
"""
混合适应度评估实现验证脚本

该脚本验证混合适应度评估功能的正确实现，包括：
1. 配置文件验证
2. 代码导入验证
3. 基本功能验证
4. 向后兼容性验证
5. 命令行参数验证
"""

import os
import sys
import yaml
import subprocess
import tempfile
import numpy as np
from pathlib import Path

def check_config_file():
    """检查配置文件是否正确添加了混合适应度评估配置"""
    print("🔍 检查配置文件...")
    
    try:
        with open('config/config.yaml', 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        # 检查混合适应度配置是否存在
        hybrid_config = config.get('pso', {}).get('hybrid_fitness', {})
        
        if not hybrid_config:
            print("❌ 配置文件中缺少 hybrid_fitness 配置")
            return False
        
        # 检查必要的配置项
        required_keys = ['enable', 'classifier_weight', 'statistical_weight']
        for key in required_keys:
            if key not in hybrid_config:
                print(f"❌ 配置文件中缺少 {key} 配置")
                return False
        
        print("✅ 配置文件检查通过")
        print(f"   启用状态: {hybrid_config.get('enable')}")
        print(f"   分类器权重: {hybrid_config.get('classifier_weight')}")
        print(f"   统计学权重: {hybrid_config.get('statistical_weight')}")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置文件检查失败: {e}")
        return False


def check_file_structure():
    """检查文件结构是否正确"""
    print("\n🔍 检查文件结构...")
    
    required_files = [
        'src/hybrid_fitness_evaluator.py',
        'config/config.yaml',
        'main.py',
        'test_hybrid_fitness.py',
        'example_hybrid_usage.py',
        'HYBRID_FITNESS_IMPLEMENTATION.md',
        'USAGE_EXAMPLES.md'
    ]
    
    missing_files = []
    for file_path in required_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
    
    if missing_files:
        print("❌ 缺少以下文件:")
        for file_path in missing_files:
            print(f"   - {file_path}")
        return False
    
    print("✅ 文件结构检查通过")
    return True


def check_imports():
    """检查代码导入是否正确"""
    print("\n🔍 检查代码导入...")
    
    try:
        # 添加src目录到路径
        sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))
        
        # 测试导入
        from hybrid_fitness_evaluator import HybridFitnessEvaluator
        print("✅ HybridFitnessEvaluator 导入成功")
        
        from advanced_pso_optimizer import AdvancedPSOOptimizer
        print("✅ AdvancedPSOOptimizer 导入成功")
        
        # 检查新方法是否存在
        optimizer = AdvancedPSOOptimizer(enable_hybrid_fitness=False)
        
        required_methods = [
            'set_hybrid_fitness_weights',
            'enable_hybrid_fitness_evaluation',
            'get_hybrid_fitness_stats',
            'clear_hybrid_fitness_cache'
        ]
        
        for method_name in required_methods:
            if not hasattr(optimizer, method_name):
                print(f"❌ AdvancedPSOOptimizer 缺少方法: {method_name}")
                return False
        
        print("✅ 新方法检查通过")
        return True
        
    except Exception as e:
        print(f"❌ 导入检查失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def check_basic_functionality():
    """检查基本功能是否正常"""
    print("\n🔍 检查基本功能...")
    
    try:
        from hybrid_fitness_evaluator import HybridFitnessEvaluator
        from advanced_pso_optimizer import AdvancedPSOOptimizer
        
        # 创建测试序列
        test_sequence = np.linspace(20, 150, 1000)
        reference_sequences = [np.linspace(25, 145, 1000) for _ in range(3)]
        
        # 测试混合适应度评估器
        evaluator = HybridFitnessEvaluator(
            config_path="config/config.yaml",
            model_dir="models",  # 可能不存在，但应该能处理
            reference_sequences=reference_sequences
        )
        
        fitness, breakdown = evaluator.evaluate_hybrid_fitness(test_sequence)
        
        if not isinstance(fitness, (int, float)):
            print(f"❌ 适应度返回类型错误: {type(fitness)}")
            return False
        
        if not isinstance(breakdown, dict):
            print(f"❌ 分解返回类型错误: {type(breakdown)}")
            return False
        
        print(f"✅ 混合适应度评估器测试通过")
        print(f"   适应度: {fitness:.4f}")
        print(f"   分类器可用: {breakdown.get('classifier_available', False)}")
        
        # 测试PSO优化器集成
        optimizer = AdvancedPSOOptimizer(
            config_path="config/config.yaml",
            enable_hybrid_fitness=True
        )
        
        # 测试权重设置
        optimizer.set_hybrid_fitness_weights(0.7, 0.3)
        print("✅ 权重设置测试通过")
        
        # 测试统计获取
        stats = optimizer.get_hybrid_fitness_stats()
        if not isinstance(stats, dict):
            print(f"❌ 统计信息返回类型错误: {type(stats)}")
            return False
        
        print("✅ 统计信息获取测试通过")
        
        return True
        
    except Exception as e:
        print(f"❌ 基本功能检查失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def check_backward_compatibility():
    """检查向后兼容性"""
    print("\n🔍 检查向后兼容性...")
    
    try:
        from advanced_pso_optimizer import AdvancedPSOOptimizer
        
        # 测试禁用混合适应度评估
        optimizer = AdvancedPSOOptimizer(
            config_path="config/config.yaml",
            enable_hybrid_fitness=False
        )
        
        # 创建测试序列
        test_sequence = np.linspace(20, 150, 1000)
        
        # 测试原始的统计学适应度函数
        fitness, breakdown = optimizer.advanced_fitness_function(test_sequence)
        
        if not isinstance(fitness, (int, float)):
            print(f"❌ 统计学适应度返回类型错误: {type(fitness)}")
            return False
        
        if not isinstance(breakdown, dict):
            print(f"❌ 统计学分解返回类型错误: {type(breakdown)}")
            return False
        
        print("✅ 向后兼容性检查通过")
        print(f"   统计学适应度: {fitness:.4f}")
        print(f"   分解组件数: {len(breakdown)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 向后兼容性检查失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def check_command_line_args():
    """检查命令行参数是否正确添加"""
    print("\n🔍 检查命令行参数...")
    
    try:
        # 测试help命令
        result = subprocess.run(
            [sys.executable, 'main.py', '--help'],
            capture_output=True,
            text=True,
            timeout=30
        )
        
        help_output = result.stdout + result.stderr
        
        # 检查新增的参数是否在帮助信息中
        required_args = [
            '--enable-hybrid-fitness',
            '--disable-hybrid-fitness',
            '--classifier-weight',
            '--statistical-weight',
            '--hybrid-model-dir'
        ]
        
        missing_args = []
        for arg in required_args:
            if arg not in help_output:
                missing_args.append(arg)
        
        if missing_args:
            print("❌ 缺少以下命令行参数:")
            for arg in missing_args:
                print(f"   - {arg}")
            return False
        
        print("✅ 命令行参数检查通过")
        return True
        
    except Exception as e:
        print(f"❌ 命令行参数检查失败: {e}")
        return False


def check_error_handling():
    """检查错误处理是否正确"""
    print("\n🔍 检查错误处理...")
    
    try:
        from hybrid_fitness_evaluator import HybridFitnessEvaluator
        from advanced_pso_optimizer import AdvancedPSOOptimizer
        
        # 测试无效模型目录
        evaluator = HybridFitnessEvaluator(
            config_path="config/config.yaml",
            model_dir="nonexistent_directory"
        )
        
        test_sequence = np.linspace(20, 150, 1000)
        fitness, breakdown = evaluator.evaluate_hybrid_fitness(test_sequence)
        
        # 应该能够正常运行（降级到统计学方法）
        if not isinstance(fitness, (int, float)):
            print(f"❌ 错误处理失败，适应度类型错误: {type(fitness)}")
            return False
        
        print("✅ 错误处理检查通过")
        print(f"   降级适应度: {fitness:.4f}")
        print(f"   分类器可用: {breakdown.get('classifier_available', True)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 错误处理检查失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def generate_verification_report():
    """生成验证报告"""
    print("\n📋 生成验证报告...")
    
    # 运行所有检查
    checks = [
        ("配置文件", check_config_file),
        ("文件结构", check_file_structure),
        ("代码导入", check_imports),
        ("基本功能", check_basic_functionality),
        ("向后兼容性", check_backward_compatibility),
        ("命令行参数", check_command_line_args),
        ("错误处理", check_error_handling)
    ]
    
    results = {}
    for check_name, check_func in checks:
        try:
            results[check_name] = check_func()
        except Exception as e:
            print(f"❌ 检查 '{check_name}' 发生异常: {e}")
            results[check_name] = False
    
    # 生成报告
    report_content = f"""# 混合适应度评估实现验证报告

生成时间: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## 验证结果总览

"""
    
    passed = sum(results.values())
    total = len(results)
    
    for check_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        report_content += f"- {check_name}: {status}\n"
    
    report_content += f"""
## 总体结果

- 通过检查: {passed}/{total}
- 成功率: {passed/total*100:.1f}%

## 结论

"""
    
    if passed == total:
        report_content += """✅ **所有验证检查通过！**

混合适应度评估功能已成功实现，包括：
- 配置文件正确添加了相关参数
- 代码结构完整，导入正常
- 基本功能工作正常
- 保持了向后兼容性
- 命令行参数正确添加
- 错误处理机制完善

系统已准备好投入使用。
"""
    else:
        failed_checks = [name for name, result in results.items() if not result]
        report_content += f"""⚠️ **部分验证检查失败**

失败的检查项: {', '.join(failed_checks)}

请检查并修复相关问题后重新运行验证。
"""
    
    # 保存报告
    import datetime
    report_path = f"verification_report_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.md"
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write(report_content)
    
    print(f"📄 验证报告已保存: {report_path}")
    
    return passed == total


def main():
    """主函数"""
    print("🚀 混合适应度评估实现验证")
    print("=" * 60)
    
    try:
        success = generate_verification_report()
        
        print("\n" + "=" * 60)
        if success:
            print("🎉 验证完成！所有检查通过。")
            print("\n💡 下一步建议:")
            print("1. 运行完整测试: python test_hybrid_fitness.py")
            print("2. 查看使用示例: python example_hybrid_usage.py")
            print("3. 开始使用混合适应度评估功能")
        else:
            print("⚠️ 验证发现问题，请检查失败的项目。")
            print("\n🔧 故障排除:")
            print("1. 检查所有文件是否正确创建")
            print("2. 确认代码修改是否正确应用")
            print("3. 验证配置文件格式是否正确")
        
        return success
        
    except Exception as e:
        print(f"❌ 验证过程发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

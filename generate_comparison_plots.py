#!/usr/bin/env python3
"""
生成优化前后的对比图表
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
import os
import sys
from datetime import datetime

# 添加src目录到路径
sys.path.append('src')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def load_real_data():
    """加载真实数据"""
    real_sequences = []
    for sample_id in range(1, 22):
        try:
            df = pd.read_excel(f'data/Esterification/Sample_{sample_id}.xlsx', header=None)
            temp_sequence = df.iloc[:, 0].values
            temp_sequence = temp_sequence[~np.isnan(temp_sequence)]
            if len(temp_sequence) > 1000:
                real_sequences.append(temp_sequence)
        except Exception as e:
            print(f"无法加载样本 {sample_id}: {e}")
    
    return real_sequences

def load_optimization_results():
    """加载优化结果"""
    results_dir = "results"
    
    # 查找最新的优化结果文件
    csv_files = [f for f in os.listdir(results_dir) if f.startswith('advanced_temperature_sequence_') and f.endswith('.csv')]
    
    if not csv_files:
        print("未找到优化结果文件")
        return None, None
    
    # 按时间戳排序，取最新的
    csv_files.sort(reverse=True)
    latest_csv = csv_files[0]
    
    # 查找对应的优化后结果（如果存在）
    optimized_files = [f for f in csv_files if 'optimized' in f.lower()]
    
    print(f"找到优化结果文件: {latest_csv}")
    
    # 加载原始结果
    original_df = pd.read_csv(os.path.join(results_dir, latest_csv))
    original_sequence = original_df['温度(°C)'].values
    
    # 加载优化后结果（如果存在）
    optimized_sequence = None
    if optimized_files:
        optimized_files.sort(reverse=True)
        optimized_csv = optimized_files[0]
        print(f"找到优化后结果文件: {optimized_csv}")
        optimized_df = pd.read_csv(os.path.join(results_dir, optimized_csv))
        optimized_sequence = optimized_df['温度(°C)'].values
    
    return original_sequence, optimized_sequence

def create_comparison_plot(real_sequences, original_sequence, optimized_sequence=None):
    """创建对比图表"""
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    fig.suptitle('高级PSO优化前后对比分析', fontsize=16, fontweight='bold')
    
    # 选择一个代表性的真实序列进行对比
    if real_sequences:
        real_seq = real_sequences[0]  # 使用第一个样本
        # 重采样到相同长度以便对比
        real_seq_resampled = np.interp(
            np.linspace(0, len(real_seq)-1, len(original_sequence)),
            np.arange(len(real_seq)),
            real_seq
        )
    else:
        real_seq_resampled = None
    
    # 子图1: 温度序列对比
    ax1 = axes[0, 0]
    if real_seq_resampled is not None:
        ax1.plot(real_seq_resampled, label='真实数据', color='blue', alpha=0.7, linewidth=2)
    
    ax1.plot(original_sequence, label='原始PSO优化', color='red', alpha=0.8, linewidth=1.5)
    
    if optimized_sequence is not None:
        ax1.plot(optimized_sequence, label='优化后PSO', color='green', alpha=0.8, linewidth=1.5)
    
    ax1.set_title('温度序列对比')
    ax1.set_xlabel('时间点')
    ax1.set_ylabel('温度 (°C)')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 子图2: 起始阶段放大图
    ax2 = axes[0, 1]
    start_points = min(2000, len(original_sequence) // 10)
    
    if real_seq_resampled is not None:
        ax2.plot(real_seq_resampled[:start_points], label='真实数据', color='blue', alpha=0.7, linewidth=2)
    
    ax2.plot(original_sequence[:start_points], label='原始PSO优化', color='red', alpha=0.8, linewidth=1.5)
    
    if optimized_sequence is not None:
        ax2.plot(optimized_sequence[:start_points], label='优化后PSO', color='green', alpha=0.8, linewidth=1.5)
    
    ax2.set_title('起始阶段详细对比')
    ax2.set_xlabel('时间点')
    ax2.set_ylabel('温度 (°C)')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    # 子图3: 变化率对比
    ax3 = axes[1, 0]
    
    if real_seq_resampled is not None:
        real_changes = np.diff(real_seq_resampled)
        ax3.plot(real_changes, label='真实数据变化率', color='blue', alpha=0.6, linewidth=1)
    
    original_changes = np.diff(original_sequence)
    ax3.plot(original_changes, label='原始PSO变化率', color='red', alpha=0.7, linewidth=1)
    
    if optimized_sequence is not None:
        optimized_changes = np.diff(optimized_sequence)
        ax3.plot(optimized_changes, label='优化后PSO变化率', color='green', alpha=0.7, linewidth=1)
    
    ax3.set_title('温度变化率对比')
    ax3.set_xlabel('时间点')
    ax3.set_ylabel('变化率 (°C/点)')
    ax3.legend()
    ax3.grid(True, alpha=0.3)
    
    # 子图4: 统计特征对比
    ax4 = axes[1, 1]
    
    # 计算统计特征
    features = ['起始温度', '结束温度', '温度上升', '平均温度', '标准差']
    
    if real_seq_resampled is not None:
        real_stats = [
            real_seq_resampled[0],
            real_seq_resampled[-1],
            real_seq_resampled[-1] - real_seq_resampled[0],
            np.mean(real_seq_resampled),
            np.std(real_seq_resampled)
        ]
    
    original_stats = [
        original_sequence[0],
        original_sequence[-1],
        original_sequence[-1] - original_sequence[0],
        np.mean(original_sequence),
        np.std(original_sequence)
    ]
    
    x = np.arange(len(features))
    width = 0.25
    
    if real_seq_resampled is not None:
        ax4.bar(x - width, real_stats, width, label='真实数据', color='blue', alpha=0.7)
    
    ax4.bar(x, original_stats, width, label='原始PSO', color='red', alpha=0.7)
    
    if optimized_sequence is not None:
        optimized_stats = [
            optimized_sequence[0],
            optimized_sequence[-1],
            optimized_sequence[-1] - optimized_sequence[0],
            np.mean(optimized_sequence),
            np.std(optimized_sequence)
        ]
        ax4.bar(x + width, optimized_stats, width, label='优化后PSO', color='green', alpha=0.7)
    
    ax4.set_title('统计特征对比')
    ax4.set_xlabel('特征')
    ax4.set_ylabel('数值')
    ax4.set_xticks(x)
    ax4.set_xticklabels(features, rotation=45)
    ax4.legend()
    ax4.grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    # 保存图表
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    plot_path = f"temperature_plots/Optimized_PSO_Comparison_{timestamp}.png"
    
    os.makedirs("temperature_plots", exist_ok=True)
    plt.savefig(plot_path, dpi=300, bbox_inches='tight')
    
    print(f"对比图表已保存到: {plot_path}")
    
    return plot_path

def main():
    """主函数"""
    print("生成优化前后对比图表...")
    
    # 加载数据
    print("加载真实数据...")
    real_sequences = load_real_data()
    print(f"已加载 {len(real_sequences)} 个真实数据样本")
    
    print("加载优化结果...")
    original_sequence, optimized_sequence = load_optimization_results()
    
    if original_sequence is None:
        print("未找到优化结果，无法生成对比图表")
        return
    
    print(f"原始序列长度: {len(original_sequence):,}")
    if optimized_sequence is not None:
        print(f"优化后序列长度: {len(optimized_sequence):,}")
    
    # 生成对比图表
    plot_path = create_comparison_plot(real_sequences, original_sequence, optimized_sequence)
    
    print("对比图表生成完成！")
    
    return plot_path

if __name__ == "__main__":
    try:
        plot_path = main()
    except Exception as e:
        print(f"生成对比图表时出现错误: {e}")
        import traceback
        traceback.print_exc()

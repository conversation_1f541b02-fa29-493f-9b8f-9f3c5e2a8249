# 🚀 高级PSO温度序列优化 - 快速开始指南

## 📋 概述

本项目提供了基于高级PSO算法的化学工艺温度序列优化系统。通过集成的main.py脚本，您可以轻松运行最新的优化算法。

## ⚡ 快速开始

### 1. 基础运行
```bash
# 使用高级PSO进行优化（推荐）
python main.py --mode optimize --skip-training
```

### 2. 自定义参数运行
```bash
# 高性能配置
python main.py --mode optimize --skip-training \
  --max-iterations 300 \
  --swarm-size 80 \
  --control-points 80

# 快速测试配置
python main.py --mode optimize --skip-training \
  --max-iterations 50 \
  --swarm-size 20 \
  --control-points 30
```

### 3. 生成可视化图表
```bash
# 运行优化并生成对比图表
python main.py --mode optimize --skip-training --save-plots

# 单独生成图表（如果已有结果）
python plot_advanced_results.py
```

## 📊 命令行参数详解

### 核心参数
| 参数 | 说明 | 默认值 | 示例 |
|------|------|--------|------|
| `--mode` | 执行模式 | full | `optimize`, `train`, `full` |
| `--skip-training` | 跳过训练阶段 | False | 使用现有模型 |
| `--max-iterations` | 最大迭代次数 | 200 | `--max-iterations 300` |
| `--swarm-size` | 粒子群大小 | 30 | `--swarm-size 50` |
| `--control-points` | 控制点数量 | 50 | `--control-points 80` |

### 输出控制
| 参数 | 说明 | 示例 |
|------|------|------|
| `--output-dir` | 结果输出目录 | `--output-dir my_results` |
| `--save-plots` | 保存可视化图表 | `--save-plots` |
| `--verbose` | 详细输出模式 | `--verbose` |

### 模型配置
| 参数 | 说明 | 示例 |
|------|------|------|
| `--config` | 配置文件路径 | `--config my_config.yaml` |
| `--model-dir` | 模型目录 | `--model-dir my_models` |

## 🎯 使用场景

### 场景1: 快速测试
```bash
# 5分钟内完成测试
python main.py --mode optimize --skip-training \
  --max-iterations 20 \
  --swarm-size 10 \
  --verbose
```

### 场景2: 标准优化
```bash
# 标准配置，约15-30分钟
python main.py --mode optimize --skip-training \
  --max-iterations 200 \
  --swarm-size 50
```

### 场景3: 高精度优化
```bash
# 高精度配置，约1-2小时
python main.py --mode optimize --skip-training \
  --max-iterations 500 \
  --swarm-size 100 \
  --control-points 100
```

### 场景4: 完整流程
```bash
# 包含训练的完整流程
python main.py --mode full \
  --max-iterations 200 \
  --save-plots
```

## 📈 结果分析

### 输出文件
运行完成后，在 `results/` 目录中会生成：

1. **温度序列数据**: `advanced_temperature_sequence_*.csv`
2. **优化报告**: `advanced_pso_report_*.txt`
3. **详细结果**: `advanced_pso_results_*.json`
4. **最终报告**: `final_report_*.txt`

### 可视化图表
在 `temperature_plots/` 目录中：

1. **综合对比**: `Advanced_PSO_Comprehensive_Comparison.png`
2. **项目报告**: `Enhanced_PSO_Final_Report.png`

### 关键指标
```
✅ 最佳适应度: 0.901+ (目标 > 0.9)
✅ 温度上升: 100-120°C (符合实际范围)
✅ 序列长度: 30,000-50,000 点
✅ 收敛状态: 通常在100-300次迭代内收敛
```

## 🔧 故障排除

### 常见问题

#### 1. 模型文件不存在
```
错误: 分类器模型文件不存在
解决: 确保 models/ 目录中有训练好的模型文件
```

#### 2. 内存不足
```
错误: MemoryError
解决: 减少粒子群大小或控制点数量
python main.py --mode optimize --skip-training --swarm-size 20 --control-points 30
```

#### 3. 收敛过慢
```
问题: 优化收敛很慢
解决: 增加迭代次数或调整粒子群大小
python main.py --mode optimize --skip-training --max-iterations 500
```

#### 4. 导入错误
```
错误: 无法导入高级PSO优化器
解决: 检查 src/advanced_pso_optimizer.py 文件是否存在
```

### 性能调优

#### 提升速度
```bash
# 减少计算量
python main.py --mode optimize --skip-training \
  --swarm-size 20 \
  --control-points 30 \
  --max-iterations 100
```

#### 提升质量
```bash
# 增加计算量
python main.py --mode optimize --skip-training \
  --swarm-size 80 \
  --control-points 80 \
  --max-iterations 400
```

## 📊 性能基准

### 硬件要求
- **最低配置**: 4GB RAM, 2核CPU
- **推荐配置**: 8GB RAM, 4核CPU
- **高性能配置**: 16GB RAM, 8核CPU

### 运行时间估算
| 配置 | 粒子群大小 | 迭代次数 | 预计时间 |
|------|------------|----------|----------|
| 快速测试 | 20 | 50 | 2-5分钟 |
| 标准配置 | 50 | 200 | 15-30分钟 |
| 高精度 | 100 | 500 | 1-2小时 |

## 🎯 最佳实践

### 1. 参数选择建议
- **初次使用**: 使用默认参数
- **快速验证**: 减少迭代次数到50-100
- **生产环境**: 增加粒子群大小到80-100
- **高精度需求**: 增加控制点数量到80-100

### 2. 结果验证
```bash
# 运行多次验证稳定性
for i in {1..3}; do
  python main.py --mode optimize --skip-training --output-dir results_$i
done
```

### 3. 参数调优策略
1. 先用快速配置验证算法工作正常
2. 逐步增加粒子群大小观察改进
3. 增加迭代次数直到收敛稳定
4. 最后调整控制点数量优化细节

## 📞 技术支持

### 日志分析
使用 `--verbose` 参数获取详细日志：
```bash
python main.py --mode optimize --skip-training --verbose
```

### 结果验证
查看生成的报告文件了解详细性能指标：
```bash
# 查看最新报告
ls -la results/advanced_pso_report_*.txt
```

### 可视化分析
生成对比图表进行深入分析：
```bash
python plot_advanced_results.py
python view_temperature_plots.py
```

---

## 🎉 开始优化

现在您已经了解了所有必要信息，可以开始使用高级PSO算法进行温度序列优化了！

**推荐的第一次运行命令**：
```bash
python main.py --mode optimize --skip-training --verbose --save-plots
```

这将使用默认的高性能配置，生成详细日志和可视化图表，让您全面了解优化过程和结果。

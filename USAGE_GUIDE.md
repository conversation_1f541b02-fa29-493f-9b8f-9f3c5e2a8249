# 增强PSO系统使用指南

## 目录
1. [快速开始](#快速开始)
2. [详细使用说明](#详细使用说明)
3. [配置参数详解](#配置参数详解)
4. [高级功能](#高级功能)
5. [故障排除](#故障排除)
6. [最佳实践](#最佳实践)

## 快速开始

### 1. 环境准备
```bash
# 安装依赖
pip install numpy pandas scipy scikit-learn matplotlib seaborn pyyaml openpyxl

# 验证安装
python -c "import numpy, pandas, scipy, sklearn, matplotlib, seaborn, yaml; print('所有依赖安装成功')"
```

### 2. 数据准备
确保数据目录结构正确：
```
data/
└── Esterification/
    ├── Sample_1.xlsx
    ├── Sample_2.xlsx
    ├── ...
    ├── Sample_21.xlsx
    └── label_1.xlsx
```

### 3. 基础运行
```python
from src.enhanced_pso_optimizer import EnhancedPSOOptimizer

# 创建优化器
optimizer = EnhancedPSOOptimizer()

# 定义简单的适应度函数
def simple_fitness(sequence):
    temp_rise = sequence[-1] - sequence[0]
    return temp_rise / 100.0  # 偏好温度上升

# 运行优化
results = optimizer.optimize_enhanced(simple_fitness)
print(f"最佳适应度: {results['best_fitness']:.6f}")
```

## 详细使用说明

### 1. 业务数据分析

#### 基础分析
```python
from src.business_data_analyzer import BusinessDataAnalyzer

# 创建分析器
analyzer = BusinessDataAnalyzer()

# 加载数据
sequences = analyzer.load_all_temperature_data()
labels = analyzer.load_quality_labels()

print(f"加载了 {len(sequences)} 个温度序列")
print(f"质量标签范围: {labels['label_1'].min():.4f} - {labels['label_1'].max():.4f}")
```

#### 完整分析流程
```python
# 运行完整分析
analysis_results = analyzer.run_complete_analysis()

# 查看关键结果
basic_stats = analysis_results['basic_statistics']['global_stats']
print(f"全局温度范围: {basic_stats['global_min']:.2f} - {basic_stats['global_max']:.2f}°C")
print(f"平均温度: {basic_stats['global_mean']:.2f}°C")

# 保存分析结果
analyzer.save_analysis_results("my_analysis_results")
analyzer.generate_analysis_report("my_analysis_results")
```

### 2. 数据驱动初始化

#### 基础使用
```python
from src.data_driven_initializer import DataDrivenInitializer

# 创建初始化器
initializer = DataDrivenInitializer()

# 生成数据驱动的粒子群
swarm_size = 30
swarm = initializer.generate_data_driven_swarm(swarm_size)

print(f"生成了 {len(swarm)} 个数据驱动的粒子")

# 验证粒子有效性
valid_count = sum(1 for particle in swarm if initializer.validate_particle(particle))
print(f"有效粒子: {valid_count}/{len(swarm)}")
```

#### 自定义参数
```python
# 调整初始化参数
initializer.set_initialization_parameters(
    mutation_strength=0.15,    # 变异强度
    perturbation_ratio=0.25,   # 扰动比例
    diversity_factor=0.3       # 多样性因子
)

# 获取统计信息
stats = initializer.get_initialization_statistics()
print("初始化统计信息:", stats)
```

### 3. 增强约束系统

#### 基础约束应用
```python
from src.enhanced_constraints import EnhancedConstraints
import numpy as np

# 创建约束系统
constraints = EnhancedConstraints()

# 创建测试序列
test_sequence = np.linspace(20, 150, 1000) + np.random.normal(0, 5, 1000)

# 应用所有约束
constrained_sequence = constraints.apply_all_constraints(test_sequence)

# 验证约束
validation = constraints.validate_sequence_constraints(constrained_sequence)
print(f"约束验证: {'通过' if validation['valid'] else '失败'}")
print(f"约束分数: {validation['constraint_scores']}")
```

#### 选择性约束应用
```python
# 只应用特定约束
constraint_types = ['basic_bounds', 'business_trend', 'statistical_features']
constrained_sequence = constraints.apply_all_constraints(
    test_sequence, 
    constraint_types=constraint_types
)
```

### 4. 增强PSO优化

#### 完整优化流程
```python
from src.enhanced_pso_optimizer import EnhancedPSOOptimizer

# 创建增强PSO优化器
optimizer = EnhancedPSOOptimizer(
    config_path="config/config.yaml",
    enable_business_analysis=True
)

# 定义复杂的适应度函数
def advanced_fitness(sequence):
    # 温度上升奖励
    temp_rise = sequence[-1] - sequence[0]
    rise_score = min(temp_rise / 120.0, 1.0)  # 期望上升120°C
    
    # 平滑性奖励
    diff = np.diff(sequence)
    smoothness = 1.0 / (1.0 + np.std(diff))
    
    # 目标温度范围奖励
    mean_temp = np.mean(sequence)
    temp_score = 1.0 - abs(mean_temp - 130.0) / 50.0
    temp_score = max(0, temp_score)
    
    # 综合评分
    return 0.5 * rise_score + 0.3 * smoothness + 0.2 * temp_score

# 执行优化
results = optimizer.optimize_enhanced(advanced_fitness)

# 查看详细结果
print(f"最佳适应度: {results['best_fitness']:.6f}")
print(f"迭代次数: {results['total_iterations']}")
print(f"是否收敛: {'是' if results['converged'] else '否'}")

if 'fitness_breakdown' in results:
    breakdown = results['fitness_breakdown']
    print("适应度分解:")
    for component, score in breakdown.items():
        if component != 'weights':
            print(f"  {component}: {score:.4f}")
```

#### 获取增强功能摘要
```python
# 查看增强功能状态
summary = optimizer.get_enhancement_summary()
print("增强功能摘要:")
for category, details in summary.items():
    print(f"  {category}: {details}")
```

### 5. 对比分析

#### 基础对比
```python
from src.comparison_analyzer import ComparisonAnalyzer
from src.pso_optimizer import PSOOptimizer

# 运行基线PSO
baseline_optimizer = PSOOptimizer()
baseline_results = baseline_optimizer.optimize(advanced_fitness)

# 运行增强PSO
enhanced_optimizer = EnhancedPSOOptimizer()
enhanced_results = enhanced_optimizer.optimize_enhanced(advanced_fitness)

# 创建对比分析器
analyzer = ComparisonAnalyzer(output_dir="comparison_results")

# 运行完整对比分析
comparison_summary = analyzer.run_complete_comparison(
    baseline_results, 
    enhanced_results
)

print("对比分析完成!")
print(f"输出目录: {comparison_summary['output_directory']}")
```

#### 查看对比结果
```python
# 获取改进摘要
improvement = comparison_summary['comparison_results']['improvement_summary']
print(f"整体改进: {'是' if improvement['overall_improvement'] else '否'}")
print(f"推荐: {improvement['recommendation']}")

# 查看适应度改进
fitness_comp = comparison_summary['comparison_results']['fitness_comparison']
print(f"适应度改进: {fitness_comp['percentage_improvement']:.2f}%")
```

## 配置参数详解

### config.yaml 主要参数

#### 数据配置
```yaml
data:
  data_dir: "data/Esterification"           # 数据目录
  sample_file_pattern: "Sample_{}.xlsx"     # 样本文件命名模式
  label_files:
    label_1: "label_1.xlsx"                 # 质量标签文件
```

#### PSO基础参数
```yaml
pso:
  swarm_size: 30                            # 粒子群大小
  max_iterations: 100                       # 最大迭代次数
  w_start: 0.9                             # 初始惯性权重
  w_end: 0.4                               # 最终惯性权重
  c1: 2.0                                  # 个体学习因子
  c2: 2.0                                  # 社会学习因子
  tolerance: 1e-6                          # 收敛容差
  patience: 20                             # 早停耐心值
```

#### 温度序列参数
```yaml
temperature_sequence:
  control_points: 30                        # 控制点数量
  sequence_length: 50000                    # 序列长度
  min_temperature: 13.0                     # 最低温度
  max_temperature: 152.0                    # 最高温度
  max_change_rate: 0.1                     # 最大变化率
  
  variable_length:                          # 变长序列配置
    enable: true                            # 是否启用
    min_length: 18808                       # 最小长度
    max_length: 92002                       # 最大长度
    default_length: 50000                   # 默认长度
    length_weight: 0.1                      # 长度权重
```

## 高级功能

### 1. 自定义适应度评估器
```python
from src.enhanced_fitness_evaluator import EnhancedFitnessEvaluator

# 如果有训练好的分类器
# evaluator = EnhancedFitnessEvaluator(
#     classifier=your_classifier,
#     feature_extractor=your_feature_extractor,
#     reference_sequences=real_sequences
# )

# 调整适应度权重
# evaluator.set_fitness_weights(
#     quality_score=0.4,
#     similarity_score=0.3,
#     trend_consistency=0.2,
#     statistical_match=0.1
# )
```

### 2. 批量优化
```python
def run_batch_optimization(fitness_functions, num_runs=5):
    """运行批量优化实验"""
    results = []
    
    for i, fitness_func in enumerate(fitness_functions):
        print(f"运行实验 {i+1}/{len(fitness_functions)}")
        
        for run in range(num_runs):
            optimizer = EnhancedPSOOptimizer()
            result = optimizer.optimize_enhanced(fitness_func)
            result['experiment_id'] = i
            result['run_id'] = run
            results.append(result)
    
    return results

# 定义多个适应度函数
fitness_functions = [
    lambda seq: seq[-1] - seq[0],  # 简单上升
    advanced_fitness,              # 复杂评估
    # 添加更多函数...
]

# 运行批量实验
batch_results = run_batch_optimization(fitness_functions)
```

### 3. 参数敏感性分析
```python
def parameter_sensitivity_analysis():
    """参数敏感性分析"""
    swarm_sizes = [20, 30, 40, 50]
    max_iterations = [50, 100, 150, 200]
    
    results = []
    
    for swarm_size in swarm_sizes:
        for max_iter in max_iterations:
            optimizer = EnhancedPSOOptimizer()
            optimizer.swarm_size = swarm_size
            optimizer.max_iterations = max_iter
            
            result = optimizer.optimize_enhanced(advanced_fitness)
            result['swarm_size'] = swarm_size
            result['max_iterations'] = max_iter
            results.append(result)
    
    return results
```

## 故障排除

### 常见问题

#### 1. 数据加载失败
```
错误: FileNotFoundError: Sample_1.xlsx not found
解决: 检查数据目录路径和文件命名是否正确
```

#### 2. 内存不足
```
错误: MemoryError during optimization
解决: 减少粒子群大小或序列长度
```

#### 3. 收敛缓慢
```
问题: 优化收敛很慢或不收敛
解决: 
- 调整惯性权重范围
- 增加粒子群大小
- 检查适应度函数设计
```

#### 4. 约束冲突
```
问题: 约束验证失败
解决:
- 检查温度边界设置
- 调整约束权重
- 验证业务规则合理性
```

### 调试技巧

#### 1. 启用详细日志
```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

#### 2. 检查中间结果
```python
# 在优化过程中检查粒子状态
for i, particle in enumerate(optimizer.swarm):
    print(f"粒子 {i}: 位置={particle.position[:5]}..., 适应度={particle.fitness}")
```

#### 3. 可视化优化过程
```python
import matplotlib.pyplot as plt

# 绘制适应度历史
plt.figure(figsize=(10, 6))
plt.plot(results['best_fitness_history'])
plt.title('适应度收敛曲线')
plt.xlabel('迭代次数')
plt.ylabel('最佳适应度')
plt.grid(True)
plt.show()
```

## 最佳实践

### 1. 适应度函数设计
- **归一化**: 确保不同组件的分数在相似范围内
- **平衡性**: 避免某个组件过度主导
- **可解释性**: 确保适应度分数有明确的业务含义

```python
def well_designed_fitness(sequence):
    # 归一化的温度上升分数 (0-1)
    temp_rise = sequence[-1] - sequence[0]
    rise_score = np.clip(temp_rise / 120.0, 0, 1)
    
    # 归一化的平滑性分数 (0-1)
    diff = np.diff(sequence)
    smoothness = 1.0 / (1.0 + np.std(diff) / 10.0)
    
    # 加权组合
    return 0.7 * rise_score + 0.3 * smoothness
```

### 2. 参数调优策略
- **从小规模开始**: 先用小的粒子群和迭代次数测试
- **逐步增加**: 确认算法工作后再增加规模
- **记录实验**: 保存每次实验的参数和结果

### 3. 性能优化
- **缓存机制**: 启用适应度缓存
- **并行计算**: 考虑并行评估适应度
- **内存管理**: 及时清理不需要的数据

```python
# 启用缓存
optimizer.enhanced_fitness_evaluator.cache_enabled = True

# 定期清理缓存
if len(optimizer.enhanced_fitness_evaluator.fitness_cache) > 10000:
    optimizer.enhanced_fitness_evaluator.clear_cache()
```

### 4. 结果验证
- **多次运行**: 进行多次独立运行验证稳定性
- **统计分析**: 分析结果的统计显著性
- **业务验证**: 确保结果符合实际业务需求

```python
# 多次运行验证
results_list = []
for i in range(10):
    result = optimizer.optimize_enhanced(fitness_function)
    results_list.append(result['best_fitness'])

print(f"平均适应度: {np.mean(results_list):.6f}")
print(f"标准差: {np.std(results_list):.6f}")
print(f"最佳结果: {np.max(results_list):.6f}")
```

---

更多详细信息请参考 [README_Enhanced_PSO.md](README_Enhanced_PSO.md) 和源代码注释。
